

'use client';

import React, { useState, useEffect, useTransition, useMemo } from 'react';
import type { Mission, AssignedMission, StageType, Exploit } from '@/lib/types';
import { allMissions } from '@/data/missions';
import { useToast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogHeader, AlertDialogTitle, AlertDialogFooter, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Trash2, CheckCircle, Plus, Camera, CircleDot, Check, Trophy, Wind, Fish, BookOpen, Map, Gamepad2, Users, GraduationCap, Waves, Shield, Microscope, LandPlot, Compass } from 'lucide-react';
import { allExploits } from '@/data/exploits';


interface MissionsTabProps {
    stageId: number;
    stageType: StageType;
    stageThemes: string[];
}

type MissionLog = {
    mission_id: string;
    completed_at: string;
};

export function MissionsTab({ stageId, stageType, stageThemes }: MissionsTabProps) {
    const { toast } = useToast();
    const [isProcessing, startTransition] = useTransition();

    const [assignedMissions, setAssignedMissions] = useState<AssignedMission[]>([]);
    const [justCompletedExploits, setJustCompletedExploits] = useState<Exploit[]>([]);
    const [missionToProve, setMissionToProve] = useState<{assignedMission: AssignedMission, mission: Mission} | null>(null);

    useEffect(() => {
        const storedAssignedMissions = localStorage.getItem(`assigned_missions_${stageId}`);
        setAssignedMissions(storedAssignedMissions ? JSON.parse(storedAssignedMissions) : []);
    }, [stageId]);

    const assignedMissionIds = useMemo(() => new Set(assignedMissions.map(am => am.mission_id)), [assignedMissions]);
    
    const availableMissions = useMemo(() => {
        return allMissions.filter(mission => {
            const isAssigned = assignedMissionIds.has(mission.id);
            if (isAssigned) return false;

            if (stageThemes.length === 0) {
                 return mission.stage_type.includes(stageType);
            }
            
            const hasCommonTheme = mission.tags_theme.some(theme => stageThemes.includes(theme));
            return hasCommonTheme && mission.stage_type.includes(stageType);
        });
    }, [assignedMissionIds, stageType, stageThemes]);

    const assignedMissionsDetails = useMemo(() => {
        return assignedMissions
            .map(am => {
                const details = allMissions.find(m => m.id === am.mission_id);
                return details ? { ...am, details } : null;
            })
            .filter((am): am is AssignedMission & { details: Mission } => am !== null);
    }, [assignedMissions]);


    const handleAssignMission = (missionId: string) => {
        startTransition(() => {
            const missionToAdd = allMissions.find(e => e.id === missionId);
            if (!missionToAdd) return;
            
            const newAssignedMission: AssignedMission = {
                id: Date.now(),
                stage_id: stageId,
                mission_id: missionId,
                status: 'en_cours',
                completed_at: null,
                preuve_url: null
            };
            const updatedMissions = [...assignedMissions, newAssignedMission];
            setAssignedMissions(updatedMissions);
            localStorage.setItem(`assigned_missions_${stageId}`, JSON.stringify(updatedMissions));
            toast({ title: "Mission assignée" });
        });
    };

    const handleUnassignMission = (assignedMissionId: number) => {
        startTransition(() => {
            const updatedMissions = assignedMissions.filter(am => am.id !== assignedMissionId);
            setAssignedMissions(updatedMissions);
            localStorage.setItem(`assigned_missions_${stageId}`, JSON.stringify(updatedMissions));
            toast({ title: "Mission retirée" });
        });
    };

    const handleUpdateMission = (assignedMission: AssignedMission, completed: boolean, preuveUrl?: string) => {
        startTransition(() => {
            const missionDetails = allMissions.find(m => m.id === assignedMission.mission_id);
            if (!missionDetails) return;

            let justCompletedNow = false;
            
            const updatedMissions = assignedMissions.map(am => {
                if (am.id === assignedMission.id) {
                     const wasCompleted = am.status === 'complete';
                     if (completed && !wasCompleted) {
                        justCompletedNow = true;
                     }
                    return {
                        ...am,
                        status: completed ? 'complete' : 'en_cours',
                        completed_at: completed ? new Date().toISOString() : null,
                        preuve_url: preuveUrl !== undefined ? preuveUrl : am.preuve_url,
                    };
                }
                return am;
            });
            
            setAssignedMissions(updatedMissions);
            localStorage.setItem(`assigned_missions_${stageId}`, JSON.stringify(updatedMissions));

            if (justCompletedNow) {
                 // Add mission to user's global log
                const storedLog = localStorage.getItem('user_completed_missions');
                const currentLog: MissionLog[] = storedLog ? JSON.parse(storedLog) : [];
                const newLogEntry: MissionLog = { mission_id: assignedMission.mission_id, completed_at: new Date().toISOString() };
                const newLog = [...currentLog, newLogEntry];
                localStorage.setItem('user_completed_missions', JSON.stringify(newLog));

                toast({ title: `Mission terminée !`, description: `Bravo !` });

                // Check for new exploits
                 const missionCounts: Record<string, number> = newLog.reduce((acc, mission) => {
                    acc[mission.mission_id] = (acc[mission.mission_id] || 0) + 1;
                    return acc;
                }, {} as Record<string, number>);

                const unlockedExploits = allExploits.filter(exploit => {
                    const currentCount = missionCounts[exploit.condition.mission_id] || 0;
                    // Check if the exploit is unlocked with this completion
                    return currentCount === exploit.condition.count;
                });

                if (unlockedExploits.length > 0) {
                    setJustCompletedExploits(unlockedExploits);
                }

            } else {
                 toast({ title: "Progression mise à jour" });
            }
            
            setMissionToProve(null);
        });
    };
    
    const iconMap: { [key: string]: React.ElementType } = {
        Shield, Trash2, Wind, Fish, Map, Gamepad2, BookOpen, Trophy, Camera, Microscope, LandPlot, Compass, Waves
    };

    const MissionCard = ({ mission, children }: { mission: Mission, children: React.ReactNode }) => {
        const IconComponent = iconMap[mission.icon] || Shield;
        return (
            <AccordionItem value={mission.id} className="border-b-0">
                <Card className="p-0">
                    <AccordionTrigger className="p-4 hover:no-underline">
                        <div className="flex items-start gap-4 text-left">
                            <IconComponent className="w-8 h-8 text-primary mt-1 shrink-0" />
                            <div className="flex-grow">
                                <p className="font-semibold">{mission.description}</p>
                            </div>
                        </div>
                    </AccordionTrigger>
                    <AccordionContent className="px-4 pb-4">
                        <div className="border-t pt-4">
                            {children}
                        </div>
                    </AccordionContent>
                </Card>
            </AccordionItem>
        );
    };

    return (
        <div className="space-y-8">
            <Card>
                <CardHeader>
                    <CardTitle>Missions Assignées</CardTitle>
                    <CardDescription>Les défis sélectionnés pour ce stage. Validez chaque mission une fois complétée avec votre groupe.</CardDescription>
                </CardHeader>
                <CardContent>
                    <Accordion type="multiple" className="space-y-3">
                        {assignedMissionsDetails.length > 0 ? assignedMissionsDetails.map(assignedMission => {
                            const missionDetails = assignedMission.details;
                            if (!missionDetails) return null;
                            const isCompleted = assignedMission.status === 'complete';

                            return (
                                <MissionCard key={assignedMission.id} mission={missionDetails}>
                                    <div className="flex justify-between items-center mb-4">
                                        <Badge variant={isCompleted ? "default" : "secondary"}>
                                            {isCompleted ? <CheckCircle className="w-4 h-4 mr-2"/> : null}
                                            {isCompleted ? "Mission Terminée !" : "En cours..."}
                                        </Badge>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button size="sm" variant="ghost" className="text-destructive" disabled={isProcessing}>
                                                    <Trash2 className="w-4 h-4 mr-2" />
                                                    Retirer
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader><AlertDialogTitle>Retirer la mission ?</AlertDialogTitle><AlertDialogDescription>La progression sera perdue.</AlertDialogDescription></AlertDialogHeader>
                                                <AlertDialogFooter><AlertDialogCancel>Annuler</AlertDialogCancel><AlertDialogAction onClick={() => handleUnassignMission(assignedMission.id)}>Confirmer</AlertDialogAction></AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="flex items-center justify-between p-3 rounded-md bg-muted/50">
                                            <div className="flex-1">
                                                <p className="text-sm font-medium leading-none">{missionDetails.instruction}</p>
                                            </div>
                                            
                                            {(missionDetails.type_preuve === 'checkbox' || missionDetails.type_preuve === 'action' || missionDetails.type_preuve === 'quiz') && (
                                                <div className="flex items-center gap-2 pl-2">
                                                    <span className="text-xs text-muted-foreground">{isCompleted ? 'Validé' : 'À faire'}</span>
                                                    <Checkbox
                                                        checked={isCompleted}
                                                        onCheckedChange={(checked) => handleUpdateMission(assignedMission, !!checked)}
                                                        disabled={isProcessing}
                                                    />
                                                </div>
                                            )}

                                            {missionDetails.type_preuve === 'photo' && (
                                                <div className="flex items-center gap-2 pl-2">
                                                    {isCompleted && assignedMission.preuve_url && (
                                                         <img src={assignedMission.preuve_url} alt={`Preuve pour ${missionDetails.description}`} width={40} height={40} className="rounded-md object-cover" />
                                                    )}
                                                    <Button size="sm" variant={isCompleted ? "secondary" : "outline"} onClick={() => setMissionToProve({ assignedMission, mission: missionDetails })} disabled={isProcessing}>
                                                        <Camera className="w-4 h-4 mr-2" />
                                                        {isCompleted ? 'Modifier' : 'Valider'}
                                                    </Button>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </MissionCard>
                            )
                        }) : (
                            <p className="text-sm text-muted-foreground text-center py-4">Aucune mission assignée. Choisissez-en dans la bibliothèque ci-dessous.</p>
                        )}
                    </Accordion>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Bibliothèque de Missions</CardTitle>
                    <CardDescription>
                        Missions disponibles pour votre type de stage et les thèmes du programme.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                     <Accordion type="multiple" className="space-y-3">
                        {availableMissions.length > 0 ? availableMissions.map(mission => {
                            return (
                                <MissionCard key={mission.id} mission={mission}>
                                    <div className="space-y-3">
                                        <div className="text-sm text-foreground p-3 bg-muted/50 rounded-lg">
                                           <p className="font-semibold mb-1">Instruction :</p>
                                           <p>{mission.instruction}</p>
                                        </div>
                                        <div className="flex justify-end pt-2">
                                            <Button size="sm" variant="outline" onClick={() => handleAssignMission(mission.id)} disabled={isProcessing}>
                                                <Plus className="w-4 h-4 mr-2" />
                                                Assigner au stage
                                            </Button>
                                        </div>
                                    </div>
                                </MissionCard>
                            )
                        }) : (
                            <p className="text-sm text-muted-foreground text-center py-4">
                                Toutes les missions disponibles pour ce type de stage ont été assignées, ou aucun thème de programme n'a été défini.
                            </p>
                        )}
                    </Accordion>
                </CardContent>
            </Card>
            
             <AlertDialog open={justCompletedExploits.length > 0} onOpenChange={() => setJustCompletedExploits([])}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <div className="text-center">
                            <motion.div
                                animate={{ scale: [1, 1.2, 1], rotate: [-5, 5, 0] }}
                                transition={{ duration: 0.5, ease: "easeInOut" }}
                            >
                                <Trophy className="w-20 h-20 text-yellow-400 mx-auto" />
                            </motion.div>
                            <AlertDialogTitle className="text-2xl mt-4">Exploit Débloqué !</AlertDialogTitle>
                            <AlertDialogDescription className="mt-2 text-lg">
                                Bravo, vous avez débloqué :<br/>
                                <span className="font-semibold text-foreground">{justCompletedExploits.map(t => t.title).join(', ')}</span>
                            </AlertDialogDescription>
                        </div>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogAction onClick={() => setJustCompletedExploits([])}>Génial !</AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
            <CameraProofModal
                missionToProve={missionToProve}
                setMissionToProve={setMissionToProve}
                onUpdateMission={(am, m, completed, url) => handleUpdateMission(am, completed, url)}
            />
        </div>
    );
}

const CameraProofModal = ({ missionToProve, setMissionToProve, onUpdateMission }: {
    missionToProve: { assignedMission: AssignedMission; mission: Mission } | null;
    setMissionToProve: (data: { assignedMission: AssignedMission; mission: Mission } | null) => void;
    onUpdateMission: (assignedMission: AssignedMission, mission: Mission, completed: boolean, preuveUrl?: string) => void;
}) => {
    const videoRef = React.useRef<HTMLVideoElement>(null);
    const canvasRef = React.useRef<HTMLCanvasElement>(null);
    const [hasCameraPermission, setHasCameraPermission] = useState<boolean | null>(null);
    const [photoDataUrl, setPhotoDataUrl] = useState<string | null>(null);
    const { toast } = useToast();

    useEffect(() => {
        const getCameraPermission = async () => {
            if (missionToProve && !photoDataUrl) {
                setHasCameraPermission(null); 
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                    setHasCameraPermission(true);
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                    }
                } catch (error) {
                    console.error('Error accessing camera:', error);
                    setHasCameraPermission(false);
                    toast({
                        variant: 'destructive',
                        title: 'Accès Caméra Refusé',
                        description: 'Veuillez autoriser l\'accès à la caméra dans les paramètres de votre navigateur.',
                    });
                }
            }
        };
        getCameraPermission();

        return () => {
            if (videoRef.current && videoRef.current.srcObject) {
                const stream = videoRef.current.srcObject as MediaStream;
                stream.getTracks().forEach(track => track.stop());
            }
        };
    }, [missionToProve, photoDataUrl, toast]);
    
    const handleClose = () => {
        setMissionToProve(null);
        setPhotoDataUrl(null);
    };

    const takePicture = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext('2d');
            context?.drawImage(video, 0, 0, canvas.width, canvas.height);
            const dataUrl = canvas.toDataURL('image/jpeg');
            setPhotoDataUrl(dataUrl);

             if (videoRef.current && videoRef.current.srcObject) {
                const stream = videoRef.current.srcObject as MediaStream;
                stream.getTracks().forEach(track => track.stop());
            }
        }
    };
    
    const handleValidate = () => {
        if (missionToProve && photoDataUrl) {
            onUpdateMission(missionToProve.assignedMission, missionToProve.mission, true, photoDataUrl);
            handleClose();
        }
    }

    if (!missionToProve) return null;

    return (
        <Dialog open={!!missionToProve} onOpenChange={handleClose}>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Preuve par Photo</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">{missionToProve.mission.instruction}</p>
                    <div className="bg-muted rounded-lg aspect-video flex items-center justify-center overflow-hidden">
                        {photoDataUrl ? (
                            <img src={photoDataUrl} alt="Aperçu de la preuve" className="object-contain" />
                        ) : (
                           <>
                            <video ref={videoRef} className="w-full aspect-video rounded-md" autoPlay muted playsInline />
                            <canvas ref={canvasRef} className="hidden" />
                           </>
                        )}
                        {hasCameraPermission === false && (
                             <Alert variant="destructive">
                                <AlertTitle>Caméra requise</AlertTitle>
                                <AlertDescription>Veuillez autoriser l'accès.</AlertDescription>
                            </Alert>
                        )}
                         {hasCameraPermission === null && (
                            <div className="text-muted-foreground">Démarrage de la caméra...</div>
                         )}
                    </div>
                     <div className="flex justify-center gap-4">
                        {photoDataUrl ? (
                            <>
                                <Button variant="outline" onClick={() => setPhotoDataUrl(null)}>
                                    <Camera className="mr-2 h-4 w-4" /> Reprendre
                                </Button>
                                <Button onClick={handleValidate}>
                                    <Check className="mr-2 h-4 w-4" /> Valider la mission
                                </Button>
                            </>
                        ) : (
                            <Button onClick={takePicture} disabled={!hasCameraPermission}>
                                <CircleDot className="mr-2 h-4 w-4" /> Prendre la photo
                            </Button>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
