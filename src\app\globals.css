@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 13% 18%; /* Dark Slate Gray */
    --foreground: 210 20% 96%; /* Off-white */
    
    --card: 220 13% 22%; /* Slightly lighter dark gray for cards */
    --card-foreground: 210 20% 96%;

    --popover: 220 13% 22%;
    --popover-foreground: 210 20% 96%;
    
    --primary: 190 85% 55%; /* Vibrant <PERSON><PERSON> */
    --primary-foreground: 220 13% 10%;
    
    --secondary: 220 13% 30%; /* Lighter gray for secondary elements */
    --secondary-foreground: 210 20% 96%;
    
    --muted: 220 13% 30%; /* Tabs background */
    --muted-foreground: 210 20% 75%;
    
    --accent: 190 85% 55%; /* Using primary for accent */
    --accent-foreground: 220 13% 10%;
    
    --destructive: 0 72% 51%;
    --destructive-foreground: 210 20% 96%;

    --border: 220 13% 30%;
    --input: 220 13% 25%;
    --ring: 190 85% 60%;

    --chart-1: 190 85% 55%;
    --chart-2: 220 85% 65%;
    --chart-3: 160 70% 50%;
    --chart-4: 280 70% 60%;
    --chart-5: 340 80% 60%;
    --radius: 0.8rem;

    /* Custom Sidebar Colors */
    --sidebar-background: 220 13% 14%; /* Even darker for sidebar */
    --sidebar-foreground: 210 20% 96%;
    --sidebar-primary: 190 85% 55%;
    --sidebar-primary-foreground: 220 13% 10%;
    --sidebar-accent: 220 13% 25%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 220 13% 25%;
    --sidebar-ring: 190 85% 60%;

    /* COP Colors */
    --cop-comprendre: 45 95% 55%; /* Yellow */
    --cop-observer: 210 85% 60%; /* Blue */
    --cop-proteger: 145 70% 50%; /* Green */
  }

  .dark {
    /* Keep dark theme consistent with the new design */
    --background: 220 13% 18%;
    --foreground: 210 20% 96%;
    --card: 220 13% 22%;
    --card-foreground: 210 20% 96%;
    --popover: 220 13% 22%;
    --popover-foreground: 210 20% 96%;
    --primary: 190 85% 55%;
    --primary-foreground: 220 13% 10%;
    --secondary: 220 13% 30%;
    --secondary-foreground: 210 20% 96%;
    --muted: 220 13% 30%;
    --muted-foreground: 210 20% 75%;
    --accent: 190 85% 55%;
    --accent-foreground: 220 13% 10%;
    --destructive: 0 72% 51%;
    --destructive-foreground: 210 20% 96%;
    --border: 220 13% 30%;
    --input: 220 13% 25%;
    --ring: 190 85% 60%;
    
    --chart-1: 190 85% 55%;
    --chart-2: 220 85% 65%;
    --chart-3: 160 70% 50%;
    --chart-4: 280 70% 60%;
    --chart-5: 340 80% 60%;

    /* Custom Sidebar Colors */
    --sidebar-background: 220 13% 14%;
    --sidebar-foreground: 210 20% 96%;
    --sidebar-primary: 190 85% 55%;
    --sidebar-primary-foreground: 220 13% 10%;
    --sidebar-accent: 220 13% 25%;
    --sidebar-accent-foreground: 210 20% 98%;
    --sidebar-border: 220 13% 25%;
    --sidebar-ring: 190 85% 60%;

    /* COP Colors */
    --cop-comprendre: 45 95% 55%; /* Yellow */
    --cop-observer: 210 85% 60%; /* Blue */
    --cop-proteger: 145 70% 50%; /* Green */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bg-cop-comprendre { @apply bg-[hsl(var(--cop-comprendre))]; }
  .text-cop-comprendre { @apply text-[hsl(var(--cop-comprendre))]; }
  .border-cop-comprendre { @apply border-[hsl(var(--cop-comprendre))]; }
  
  .bg-cop-observer { @apply bg-[hsl(var(--cop-observer))]; }
  .text-cop-observer { @apply text-[hsl(var(--cop-observer))]; }
  .border-cop-observer { @apply border-[hsl(var(--cop-observer))]; }

  .bg-cop-proteger { @apply bg-[hsl(var(--cop-proteger))]; }
  .text-cop-proteger { @apply text-[hsl(var(--cop-proteger))]; }
  .border-cop-proteger { @apply border-[hsl(var(--cop-proteger))]; }

  .bg-chart-1 { @apply bg-[hsl(var(--chart-1))]; }
  .bg-chart-2 { @apply bg-[hsl(var(--chart-2))]; }
  .bg-chart-3 { @apply bg-[hsl(var(--chart-3))]; }
  .bg-chart-4 { @apply bg-[hsl(var(--chart-4))]; }
  .bg-chart-5 { @apply bg-[hsl(var(--chart-5))]; }

  .bg-stage-header {
    background-image: url('/assets/BG01.jpg');
  }
}
