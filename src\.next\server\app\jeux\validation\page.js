(()=>{var e={};e.id=507,e.ids=[507],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13189:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var a=t(57307),s=t(6174);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim();var o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,s.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:i="",children:d,iconNode:l,...u},p)=>(0,s.createElement)("svg",{ref:p,...o,width:r,height:r,stroke:e,strokeWidth:a?24*Number(t)/Number(r):t,className:n("lucide",i),...u},[...l.map(([e,r])=>(0,s.createElement)(e,r)),...Array.isArray(d)?d:[d]])),l=((e,r)=>{let t=(0,s.forwardRef)(({className:t,...a},o)=>(0,s.createElement)(d,{ref:o,iconNode:r,className:n(`lucide-${i(e)}`,t),...a}));return t.displayName=`${e}`,t})("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var u=t(20846);function p(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsxs)("div",{className:"flex flex-col gap-4 justify-center items-center h-64",children:[(0,a.jsx)(l,{className:"w-8 h-8 animate-spin text-primary"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Chargement du quiz..."})]}),children:(0,a.jsx)(u.default,{})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31326:(e,r,t)=>{Promise.resolve().then(t.bind(t,73052))},32242:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>l});var a=t(5853),s=t(60554),i=t(30708),n=t.n(i),o=t(8067),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["jeux",{children:["validation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13189)),"C:\\PL\\COPUN-V5\\src\\app\\jeux\\validation\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\PL\\COPUN-V5\\src\\app\\jeux\\validation\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/jeux/validation/page",pathname:"/jeux/validation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},36054:(e,r,t)=>{Promise.resolve().then(t.bind(t,20846))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[683,425,808,97,824,308],()=>t(32242));module.exports=a})();