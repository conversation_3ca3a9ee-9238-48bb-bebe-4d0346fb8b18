(()=>{var e={};e.id=336,e.ids=[336],e.modules={2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},2783:(e,t,r)=>{Promise.resolve().then(r.bind(r,98019))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17146:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(5853),o=r(60554),s=r(30708),a=r.n(s),i=r(8067),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["stages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98543)),"C:\\PL\\COPUN-V5\\src\\app\\stages\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PL\\COPUN-V5\\src\\app\\stages\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/stages/page",pathname:"/stages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},43596:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},47580:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51976:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},62019:(e,t,r)=>{"use strict";r.d(t,{H:()=>o});var n=r(29490);function o(e,t){let r,o,p=t?.additionalDigits??2,h=function(e){let t,r={},n=e.split(s.dateTimeDelimiter);if(n.length>2)return r;if(/:/.test(n[0])?t=n[0]:(r.date=n[0],t=n[1],s.timeZoneDelimiter.test(r.date)&&(r.date=e.split(s.timeZoneDelimiter)[0],t=e.substr(r.date.length,e.length))),t){let e=s.timezone.exec(t);e?(r.time=t.replace(e[1],""),r.timezone=e[1]):r.time=t}return r}(e);if(h.date){let e=function(e,t){let r=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),n=e.match(r);if(!n)return{year:NaN,restDateString:""};let o=n[1]?parseInt(n[1]):null,s=n[2]?parseInt(n[2]):null;return{year:null===s?o:100*s,restDateString:e.slice((n[1]||n[2]).length)}}(h.date,p);r=function(e,t){var r,n,o,s,i,l,d,p;if(null===t)return new Date(NaN);let h=e.match(a);if(!h)return new Date(NaN);let f=!!h[4],g=c(h[1]),x=c(h[2])-1,O=c(h[3]),N=c(h[4]),v=c(h[5])-1;if(f){return(r=0,n=N,o=v,n>=1&&n<=53&&o>=0&&o<=6)?function(e,t,r){let n=new Date(0);n.setUTCFullYear(e,0,4);let o=n.getUTCDay()||7;return n.setUTCDate(n.getUTCDate()+((t-1)*7+r+1-o)),n}(t,N,v):new Date(NaN)}{let e=new Date(0);return(s=t,i=x,l=O,i>=0&&i<=11&&l>=1&&l<=(u[i]||(m(s)?29:28))&&(d=t,(p=g)>=1&&p<=(m(d)?366:365)))?(e.setUTCFullYear(t,x,Math.max(g,O)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!r||isNaN(r.getTime()))return new Date(NaN);let f=r.getTime(),g=0;if(h.time&&isNaN(g=function(e){var t,r,o;let s=e.match(i);if(!s)return NaN;let a=d(s[1]),l=d(s[2]),c=d(s[3]);return(t=a,r=l,o=c,24===t?0===r&&0===o:o>=0&&o<60&&r>=0&&r<60&&t>=0&&t<25)?a*n.s0+l*n.Cg+1e3*c:NaN}(h.time)))return new Date(NaN);if(h.timezone){if(isNaN(o=function(e){var t,r;if("Z"===e)return 0;let o=e.match(l);if(!o)return 0;let s="+"===o[1]?-1:1,a=parseInt(o[2]),i=o[3]&&parseInt(o[3])||0;return(t=0,(r=i)>=0&&r<=59)?s*(a*n.s0+i*n.Cg):NaN}(h.timezone)))return new Date(NaN)}else{let e=new Date(f+g),t=new Date(0);return t.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),t.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),t}return new Date(f+g+o)}let s={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},a=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,i=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,l=/^([+-])(\d{2})(?::?(\d{2}))?$/;function c(e){return e?parseInt(e):1}function d(e){return e&&parseFloat(e.replace(",","."))||0}let u=[31,null,31,30,31,30,31,31,30,31,30,31];function m(e){return e%400==0||e%4==0&&e%100!=0}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72519:(e,t,r)=>{Promise.resolve().then(r.bind(r,98543))},77190:()=>{},79551:e=>{"use strict";e.exports=require("url")},88808:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>o});var n=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function s({children:e}){return(0,n.jsxs)("html",{lang:"fr",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,n.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,n.jsxs)("body",{className:"font-body antialiased",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}},98019:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var n=r(28625),o=r(64996),s=r(93948),a=r.n(s),i=r(47580),l=r(51976),c=r(88808),d=r(43596),u=r(48382),m=r(44568);function p(){return(0,m.o)(Date.now())}var h=r(62019),f=r(94212);function g(e,t){return+(0,f.a)(e)<+(0,f.a)(t)}function x(e,t){let r=(0,f.a)(e),n=(0,f.a)(t);return r.getTime()>n.getTime()}var O=r(87571),N=r(19936);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/create-stage-form'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}();let v=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).flatMap(e=>e.themes),j=({stage:e})=>{let t,r=p(),s=(0,h.H)(e.start_date),u=(0,h.H)(e.end_date);t=g(u,r)?"Pass\xe9":x(s,r)?"\xc0 venir":"En cours";let[m,...f]=e.title.split(" - "),v=f.join(" - "),j=(0,o.useMemo)(()=>{let t=Object.values(e.objectivesProgress).reduce((e,t)=>(e.completed+=t.completed,e.total+=t.total,e),{completed:0,total:0});return t.total>0?t.completed/t.total*100:0},[e.objectivesProgress]),b=(0,o.useMemo)(()=>{let{completed:t,total:r}=e.missionsProgress;return r>0?t/r*100:0},[e.missionsProgress]);return(0,n.jsx)(a(),{href:`/stages/${e.id}`,className:"block group",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:shadow-lg transition-shadow h-full flex flex-col",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg font-bold group-hover:text-primary transition-colors",children:m}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())({"\xc0 venir":"bg-yellow-100 text-yellow-800 border-yellow-200","En cours":"bg-blue-100 text-blue-800 border-blue-200",Passé:"bg-gray-100 text-gray-700 border-gray-200"}[t],"shrink-0"),children:t})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center flex-wrap text-xs",children:v?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{className:"font-medium",children:v}),(0,n.jsx)("span",{className:"mx-1.5",children:"\xb7"}),(0,n.jsx)("span",{children:e.type})]}):e.type})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2 text-sm text-muted-foreground",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(i.A,{className:"w-4 h-4"}),(0,n.jsxs)("span",{children:[(0,O.GP)(s,"d MMM",{locale:N.fr})," - ",(0,O.GP)(u,"d MMM yyyy",{locale:N.fr})]})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(l.A,{className:"w-4 h-4"}),(0,n.jsxs)("span",{children:[e.participants," participants"]})]})]}),(0,n.jsxs)("div",{className:"space-y-3 pt-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mb-1",children:[(0,n.jsxs)("span",{className:"flex items-center gap-1.5",children:[(0,n.jsx)(c.A,{className:"w-3.5 h-3.5"})," Objectifs"]}),(0,n.jsxs)("span",{children:[Math.round(j),"%"]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:j,className:"h-1.5"})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mb-1",children:[(0,n.jsxs)("span",{className:"flex items-center gap-1.5",children:[(0,n.jsx)(d.A,{className:"w-3.5 h-3.5"})," Missions"]}),(0,n.jsxs)("span",{children:[e.missionsProgress.completed," / ",e.missionsProgress.total]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:b,className:"h-1.5"})]})]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-col items-start gap-3 border-t pt-4",children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"single",collapsible:!0,className:"w-full",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"details",className:"border-b-0",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:e=>e.preventDefault(),className:"text-sm font-semibold text-foreground hover:no-underline py-0",children:"Voir le d\xe9tail de la progression"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-4 space-y-2",children:Object.keys(e.objectivesProgress).length>0?Object.entries(e.objectivesProgress).map(([t,r])=>{let o=e.mainThemes.find(e=>e.title===t);if(!o)return null;let s=o.icon,a=r.total>0?r.completed/r.total*100:0;return 0===r.total?null:(0,n.jsxs)("div",{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center text-xs text-muted-foreground mb-1",children:[(0,n.jsxs)("span",{className:"flex items-center gap-1.5",children:[(0,n.jsx)(s,{className:"w-3.5 h-3.5"})," ",o.title]}),(0,n.jsxs)("span",{children:[r.completed," / ",r.total]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:a,className:"h-1.5"})]},o.id)}):(0,n.jsx)("p",{className:"text-xs text-muted-foreground text-center py-2",children:"Aucun programme d'objectifs d\xe9fini."})})]})})})]})})};function b(){let[e,t]=(0,o.useState)([]),[r,s]=(0,o.useState)(!0),[a,i]=(0,o.useTransition)(),{toast:l}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),c=async()=>{s(!0);let e=await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())();t(await Promise.all(e.map(async e=>{let t=(await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e.id)).find(e=>e.selected_content?.program?.length??!1),r={},n=[],o={completed:0,total:0};if(t&&t.selected_content.program){let o=new Set(t.selected_content.program),s=localStorage.getItem(`completed_objectives_${e.id}`),a=s?new Set(JSON.parse(s)):new Set,i=new Set;o.forEach(e=>{let t=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.find(t=>`q${t.id}`===e);t&&t.tags_theme.forEach(e=>i.add(e))}),(n=Array.from(i).map(e=>v.find(t=>t.id===e)).filter(e=>!!e)).forEach(e=>{r[e.title]={completed:0,total:0}}),o.forEach(e=>{let t=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.find(t=>`q${t.id}`===e);t&&n.forEach(n=>{t.tags_theme.includes(n.id)&&(r[n.title]||(r[n.title]={completed:0,total:0}),r[n.title].total++,a.has(e)&&r[n.title].completed++)})})}let s=localStorage.getItem(`assigned_missions_${e.id}`),a=s?JSON.parse(s):[];return o.total=a.length,o.completed=a.filter(e=>"complete"===e.status).length,{...e,objectivesProgress:r,missionsProgress:o,mainThemes:n}}))),s(!1)},{currentStages:d,upcomingStages:m,pastStages:f}=(0,o.useMemo)(()=>{let t=p();return e.sort((e,t)=>(0,h.H)(e.start_date).getTime()-(0,h.H)(t.start_date).getTime()).reduce((e,r)=>{let n=(0,h.H)(r.start_date);return g((0,h.H)(r.end_date),t)?e.pastStages.push(r):x(n,t)?e.upcomingStages.push(r):e.currentStages.push(r),e},{currentStages:[],upcomingStages:[],pastStages:[]})},[e]);return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-foreground font-headline",children:"Mes Stages"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Planifiez, suivez et g\xe9rez tous vos stages."})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/create-stage-form'");throw e.code="MODULE_NOT_FOUND",e}()),{onStageCreate:e=>{i(async()=>{await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e)?(await c(),l({title:"Stage cr\xe9\xe9",description:"Le nouveau stage a \xe9t\xe9 ajout\xe9."})):l({title:"Erreur",description:"La cr\xe9ation du stage a \xe9chou\xe9.",variant:"destructive"})})},isCreating:a})]}),r?(0,n.jsxs)("div",{className:"p-10 text-center text-muted-foreground",children:[(0,n.jsx)(u.A,{className:"mx-auto h-8 w-8 animate-spin mb-4"}),(0,n.jsx)("p",{children:"Chargement des stages..."})]}):e.length>0?(0,n.jsxs)("div",{className:"space-y-8",children:[d.length>0&&(0,n.jsxs)("section",{children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-foreground",children:"En cours"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(e=>(0,n.jsx)(j,{stage:e},e.id))})]}),m.length>0&&(0,n.jsxs)("section",{children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-foreground",children:"\xc0 venir"}),(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:m.map(e=>(0,n.jsx)(j,{stage:e},e.id))})]}),f.length>0&&(0,n.jsx)("section",{children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"single",collapsible:!0,className:"w-full",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"past-stages",className:"border-b-0",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl font-semibold hover:no-underline text-muted-foreground",children:["Stages pass\xe9s (",f.length,")"]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-4",children:f.sort((e,t)=>(0,h.H)(t.start_date).getTime()-(0,h.H)(e.start_date).getTime()).map(e=>(0,n.jsx)(j,{stage:e},e.id))})})]})})})]}):(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-10 text-center text-muted-foreground",children:[(0,n.jsx)("p",{className:"text-lg mb-2",children:"Aucun stage planifi\xe9."}),(0,n.jsx)("p",{children:'Utilisez le bouton "Nouveau Stage" pour commencer.'})]})})]})}},98543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\stages\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\stages\\page.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,425,808,97,410],()=>r(17146));module.exports=n})();