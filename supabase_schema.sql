-- Drop existing types and tables with cascade to handle dependencies
DROP TYPE IF EXISTS public.observation_category CASCADE;
DROP TYPE IF EXISTS public.card_level CASCADE;
DROP TYPE IF EXISTS public.card_status CASCADE;

DROP TABLE IF EXISTS public.content_cards CASCADE;
DROP TABLE IF EXISTS public.options CASCADE;
DROP TABLE IF EXISTS public.etages CASCADE;
DROP TABLE IF EXISTS public.structures CASCADE;
DROP TABLE IF EXISTS public.sorties CASCADE;
DROP TABLE IF EXISTS public.stages CASCADE;
DROP TABLE IF EXISTS public.observations CASCADE;

-- <PERSON><PERSON> ENUM types
CREATE TYPE public.observation_category AS ENUM ('Faune', 'Flore', 'Pollution', 'Phénomène inhabituel');
CREATE TYPE public.card_level AS ENUM ('national', 'regional', 'local', 'personal');
CREATE TYPE public.card_status AS ENUM ('validated', 'draft');


-- Create tables
CREATE TABLE public.stages (
    id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    title text NOT NULL,
    type text NOT NULL,
    participants integer NOT NULL,
    start_date date NOT NULL,
    end_date date NOT NULL
);

CREATE TABLE public.sorties (
    id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    stage_id bigint REFERENCES public.stages(id) ON DELETE CASCADE NOT NULL,
    date date NOT NULL,
    title text NOT NULL,
    themes text[] NOT NULL,
    duration integer NOT NULL,
    summary text NOT NULL,
    content text NOT NULL,
    selected_notions jsonb,
    selected_content jsonb
);

CREATE TABLE public.observations (
    id bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    category public.observation_category NOT NULL,
    observation_date date NOT NULL,
    latitude real NOT NULL,
    longitude real NOT NULL
);

CREATE TABLE public.etages (
    id text PRIMARY KEY,
    title text NOT NULL,
    icon text NOT NULL,
    color text NOT NULL,
    "order" integer NOT NULL
);

CREATE TABLE public.options (
    id text PRIMARY KEY,
    etage_id text REFERENCES public.etages(id) ON DELETE CASCADE NOT NULL,
    label text NOT NULL,
    duration integer,
    group_size text,
    activities text[],
    safety text[],
    tip text NOT NULL,
    materials text[],
    local_content text,
    "order" integer NOT NULL
);

CREATE TABLE public.content_cards (
    id text PRIMARY KEY,
    option_id text REFERENCES public.options(id) ON DELETE CASCADE NOT NULL,
    title text NOT NULL,
    description text NOT NULL,
    image text,
    "data-ai-hint" text,
    duration integer NOT NULL,
    level public.card_level NOT NULL,
    status public.card_status DEFAULT 'draft'::public.card_status NOT NULL
);

CREATE TABLE public.structures (
    id text PRIMARY KEY,
    name text NOT NULL,
    latitude real NOT NULL,
    longitude real NOT NULL
);


-- Enable Row Level Security (RLS)
ALTER TABLE public.stages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sorties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.etages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.options ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.structures ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow public access" ON public.stages;
DROP POLICY IF EXISTS "Allow public access" ON public.sorties;
DROP POLICY IF EXISTS "Allow public access" ON public.observations;
DROP POLICY IF EXISTS "Allow public access" ON public.etages;
DROP POLICY IF EXISTS "Allow public access" ON public.options;
DROP POLICY IF EXISTS "Allow public access" ON public.content_cards;
DROP POLICY IF EXISTS "Allow public access" ON public.structures;


-- Create RLS policies for public access
CREATE POLICY "Allow public access" ON public.stages FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.sorties FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.observations FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.etages FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.options FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.content_cards FOR ALL TO public USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access" ON public.structures FOR ALL TO public USING (true) WITH CHECK (true);
