
import type { Exploit } from '@/lib/types';

export const allExploits: Exploit[] = [
    {
        id: 'exploit_cartographe_junior',
        title: 'Cartographe Junior',
        description: 'Terminer 3 fois la mission "Cartographier l\'estran".',
        condition: { mission_id: 'mission_littoral_1', count: 3 },
        icon: 'Map',
    },
    {
        id: 'exploit_sentinelle_confirmee',
        title: '<PERSON>le de la Propreté',
        description: 'Terminer 5 fois la mission "Collecte de déchets ciblée".',
        condition: { mission_id: 'mission_pollution_1', count: 5 },
        icon: 'Shield',
    },
    {
        id: 'exploit_pedagogue_hors_pair',
        title: 'Animateur Pédagogique',
        description: 'Terminer 5 fois la mission "Animer un jeu pédagogique".',
        condition: { mission_id: 'mission_jeu_1', count: 5 },
        icon: 'GraduationCap',
    },
     {
        id: 'exploit_maitre_des_marees',
        title: '<PERSON><PERSON><PERSON> des <PERSON>',
        description: 'Terminer 10 fois la mission "Observer la marée".',
        condition: { mission_id: 'mission_reperes_1', count: 10 },
        icon: 'Waves',
    },
    {
        id: 'exploit_legende_copun',
        title: 'Légende de Cop\'un',
        description: 'Devenir un expert en identification d\'espèces locales en complétant 10 fois la mission.',
        condition: { mission_id: 'mission_bio_1', count: 10 },
        icon: 'Trophy',
    },
];
