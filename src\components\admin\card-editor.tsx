
'use client';

import React, { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, Save, ChevronsUpDown, Check } from 'lucide-react';

import type { ContentCard, GrandTheme } from '@/lib/types';
import { groupedThemes, jsonQuestions } from '@/data/etages';
import { cn } from '@/lib/utils';

const cardEditorSchema = z.object({
  question: z.string().min(1, "La question est requise."),
  answer: z.string().min(1, "L'objectif est requis."),
  tip: z.string().optional(),
  niveau: z.number().min(1).max(3),
  dimension: z.string().min(1, "Le pilier est requis."),
  tags_theme: z.array(z.string()).min(1, "Au moins un thème est requis."),
  tags_filtre: z.array(z.string()),
});

type CardEditorFormValues = z.infer<typeof cardEditorSchema>;

interface CardEditorProps {
  initialData?: ContentCard;
  onSave: (data: CardEditorFormValues) => void;
  isSaving: boolean;
}

const allFilterTags = Array.from(new Set(jsonQuestions.questions_copun.flatMap(q => q.tags_filtre)));
const allThemes = groupedThemes.flatMap(g => g.themes);

export function CardEditor({ initialData, onSave, isSaving }: CardEditorProps) {
  const router = useRouter();
  const [themePopoverOpen, setThemePopoverOpen] = useState(false);
  const [filterPopoverOpen, setFilterPopoverOpen] = useState(false);

  const form = useForm<CardEditorFormValues>({
    resolver: zodResolver(cardEditorSchema),
    defaultValues: {
      question: initialData?.question || '',
      answer: initialData?.answer || '',
      tip: initialData?.tip || '',
      niveau: initialData?.niveau || 1,
      dimension: initialData?.option_id.toUpperCase() || 'COMPRENDRE',
      tags_theme: initialData?.tags_theme || [],
      tags_filtre: initialData?.tags_filtre || [],
    },
  });

  const onSubmit = (data: CardEditorFormValues) => {
    onSave(data);
  };
  
  const isEditMode = !!initialData;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="flex items-center justify-between sticky top-0 bg-background/80 backdrop-blur-sm z-10 py-4 -my-4">
          <div>
            <Button variant="ghost" asChild>
                <Link href="/admin/contenu">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Retour à la liste
                </Link>
            </Button>
            <h1 className="text-2xl font-bold font-headline mt-1 ml-4">
              {isEditMode ? "Modifier la fiche" : "Créer une fiche"}
            </h1>
          </div>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
            {isEditMode ? 'Sauvegarder' : 'Créer la fiche'}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardContent className="pt-6 space-y-4">
                <FormField
                  control={form.control}
                  name="question"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-lg font-semibold">Question</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} placeholder="Saisissez la question principale..."/>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="answer"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-lg font-semibold">Objectif Pédagogique</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={4} placeholder="Décrivez l'objectif à atteindre..."/>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                 <FormField
                  control={form.control}
                  name="tip"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-lg font-semibold">Conseil du moniteur</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={3} placeholder="Ajoutez un conseil pratique ou une astuce..."/>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </div>
          <div className="lg:col-span-1 space-y-6 sticky top-24">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Statut</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="dimension"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Pilier</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger><SelectValue /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="COMPRENDRE">Comprendre</SelectItem>
                          <SelectItem value="OBSERVER">Observer</SelectItem>
                          <SelectItem value="PROTÉGER">Protéger</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="niveau"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Niveau</FormLabel>
                      <Select onValueChange={(v) => field.onChange(parseInt(v,10))} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger><SelectValue /></SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">Niveau 1</SelectItem>
                          <SelectItem value="2">Niveau 2-3</SelectItem>
                          <SelectItem value="3">Niveau 4-5</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Relations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                 <FormField
                    control={form.control}
                    name="tags_theme"
                    render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel>Thèmes Fédérateurs</FormLabel>
                             <Popover open={themePopoverOpen} onOpenChange={setThemePopoverOpen}>
                                <PopoverTrigger asChild>
                                    <FormControl>
                                    <Button variant="outline" role="combobox" className="w-full justify-between h-auto min-h-10">
                                        <div className="flex gap-1 flex-wrap">
                                            {field.value.length > 0 ? (
                                                field.value.map(themeId => (
                                                    <Badge key={themeId} variant="secondary">{allThemes.find(t => t.id === themeId)?.title}</Badge>
                                                ))
                                            ) : "Sélectionner des thèmes..."}
                                        </div>
                                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                    </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                                    <Command>
                                        <CommandInput placeholder="Rechercher un thème..." />
                                        <CommandEmpty>Aucun thème trouvé.</CommandEmpty>
                                        <CommandList>
                                        {groupedThemes.map((group) => (
                                            <CommandGroup key={group.label} heading={group.label}>
                                                {group.themes.map((theme: GrandTheme) => (
                                                    <CommandItem
                                                        key={theme.id}
                                                        value={theme.title}
                                                        onSelect={() => {
                                                            const newValue = field.value.includes(theme.id)
                                                                ? field.value.filter(id => id !== theme.id)
                                                                : [...field.value, theme.id];
                                                            field.onChange(newValue);
                                                        }}
                                                    >
                                                        <Check className={cn("mr-2 h-4 w-4", field.value.includes(theme.id) ? "opacity-100" : "opacity-0")} />
                                                        {theme.title}
                                                    </CommandItem>
                                                ))}
                                            </CommandGroup>
                                        ))}
                                        </CommandList>
                                    </Command>
                                </PopoverContent>
                            </Popover>
                            <FormMessage />
                        </FormItem>
                    )}
                 />
                 <FormField
                    control={form.control}
                    name="tags_filtre"
                    render={({ field }) => (
                        <FormItem className="flex flex-col">
                            <FormLabel>Filtres contextuels</FormLabel>
                             <Popover open={filterPopoverOpen} onOpenChange={setFilterPopoverOpen}>
                                <PopoverTrigger asChild>
                                    <FormControl>
                                    <Button variant="outline" role="combobox" className="w-full justify-between h-auto min-h-10">
                                        <div className="flex gap-1 flex-wrap">
                                            {field.value.length > 0 ? (
                                                field.value.map(tag => (
                                                    <Badge key={tag} variant="secondary">{tag}</Badge>
                                                ))
                                            ) : "Sélectionner des filtres..."}
                                        </div>
                                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                    </Button>
                                    </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                                    <Command>
                                        <CommandInput placeholder="Rechercher un filtre..." />
                                        <CommandEmpty>Aucun filtre trouvé.</CommandEmpty>
                                        <CommandList>
                                            <CommandGroup>
                                                {allFilterTags.map((tag) => (
                                                    <CommandItem
                                                        key={tag}
                                                        value={tag}
                                                        onSelect={() => {
                                                            const newValue = field.value.includes(tag)
                                                                ? field.value.filter(id => id !== tag)
                                                                : [...field.value, tag];
                                                            field.onChange(newValue);
                                                        }}
                                                    >
                                                        <Check className={cn("mr-2 h-4 w-4", field.value.includes(tag) ? "opacity-100" : "opacity-0")} />
                                                        {tag}
                                                    </CommandItem>
                                                ))}
                                            </CommandGroup>
                                        </CommandList>
                                    </Command>
                                </PopoverContent>
                            </Popover>
                            <FormMessage />
                        </FormItem>
                    )}
                 />
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </Form>
  );
}

    