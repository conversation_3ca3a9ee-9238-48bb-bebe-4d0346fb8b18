'use client';

import React, { useState } from 'react';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import { Camera as CameraIcon, X, Image as ImageIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface CameraCaptureButtonProps {
  onPhotosCaptured: (photos: string[]) => void;
  maxPhotos?: number;
  className?: string;
}

export function CameraCaptureButton({
  onPhotosCaptured,
  maxPhotos = 5,
  className
}: CameraCaptureButtonProps) {
  const [photos, setPhotos] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);

  // Check camera permissions
  const checkPermissions = async (): Promise<boolean> => {
    try {
      const permissions = await Camera.checkPermissions();
      const granted = permissions.camera === 'granted';
      setHasPermission(granted);
      return granted;
    } catch (error) {
      console.error('Error checking camera permissions:', error);
      setHasPermission(false);
      return false;
    }
  };

  // Request camera permissions
  const requestPermissions = async (): Promise<boolean> => {
    try {
      const permissions = await Camera.requestPermissions();
      const granted = permissions.camera === 'granted';
      setHasPermission(granted);
      return granted;
    } catch (error) {
      console.error('Error requesting camera permissions:', error);
      setHasPermission(false);
      return false;
    }
  };

  // Capture photo
  const capturePhoto = async () => {
    setIsLoading(true);

    try {
      // Check permissions first
      let permGranted = hasPermission;
      if (permGranted === null) {
        permGranted = await checkPermissions();
      }

      if (!permGranted) {
        permGranted = await requestPermissions();
      }

      if (!permGranted) {
        alert('Permission caméra refusée. Veuillez autoriser l\'accès à la caméra dans les paramètres de l\'application.');
        setIsLoading(false);
        return;
      }

      // Capture photo with high quality
      const image = await Camera.getPhoto({
        quality: 90,
        allowEditing: false,
        resultType: CameraResultType.Base64,
        source: CameraSource.Camera // Force camera, not gallery
      });

      if (image.base64String) {
        const newPhoto = `data:${image.format};base64,${image.base64String}`;
        const updatedPhotos = [...photos, newPhoto];

        if (updatedPhotos.length <= maxPhotos) {
          setPhotos(updatedPhotos);
          onPhotosCaptured(updatedPhotos);
        } else {
          alert(`Vous ne pouvez ajouter que ${maxPhotos} photos maximum.`);
        }
      }
    } catch (error) {
      console.error('Error capturing photo:', error);
      alert('Erreur lors de la capture de la photo. Veuillez réessayer.');
    } finally {
      setIsLoading(false);
    }
  };

  // Remove photo
  const removePhoto = (index: number) => {
    const updatedPhotos = photos.filter((_, i) => i !== index);
    setPhotos(updatedPhotos);
    onPhotosCaptured(updatedPhotos);
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Capture Button */}
      <Button
        type="button"
        variant="outline"
        onClick={capturePhoto}
        disabled={isLoading || photos.length >= maxPhotos}
        className="w-full"
      >
        <CameraIcon className="w-4 h-4 mr-2" />
        {isLoading ? 'Capture en cours...' : 'Prendre une photo'}
      </Button>

      {/* Photos Preview */}
      {photos.length > 0 && (
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <ImageIcon className="w-4 h-4" />
            <span>{photos.length} photo(s) capturée(s)</span>
          </div>

          <div className="grid grid-cols-3 gap-2">
            {photos.map((photo, index) => (
              <div key={index} className="relative group">
                <img
                  src={photo}
                  alt={`Photo ${index + 1}`}
                  className="w-full h-20 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  size="sm"
                  variant="destructive"
                  className="absolute -top-2 -right-2 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => removePhoto(index)}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Info Messages */}
      {hasPermission === false && (
        <p className="text-sm text-red-600">
          Permission caméra refusée. Les photos ne peuvent pas être prises.
        </p>
      )}

      {photos.length === 0 && (
        <p className="text-sm text-gray-500">
          Aucune photo capturée. Cliquez sur le bouton ci-dessus pour prendre une photo.
        </p>
      )}
    </div>
  );
}