-- supabase/migrations/0001_initial_schema.sql

-- Enable RLS
alter table "public"."observations" enable row level security;

-- Create Policy for observations
create policy "Enable read access for all users" on "public"."observations"
as permissive for select
to public
using (true);

create policy "Enable insert for authenticated users only" on "public"."observations"
as permissive for insert
to authenticated
with check (true);

create policy "Enable update for users based on user_id" on "public"."observations"
as permissive for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for users based on user_id" on "public"."observations"
as permissive for delete
to authenticated
using (true);


-- Create stages table
create table if not exists public.stages (
    id bigint generated by default as identity primary key,
    created_at timestamp with time zone not null default now(),
    title text not null,
    type text not null, -- "Hebdomadaire" or "Journée"
    participants integer not null,
    start_date date not null,
    end_date date not null
);
alter table "public"."stages" enable row level security;
create policy "Enable read access for all users" on "public"."stages" for select using (true);
create policy "Enable insert for authenticated users" on "public"."stages" for insert with check (true);
create policy "Enable update for authenticated users" on "public"."stages" for update using (true);
create policy "Enable delete for authenticated users" on "public"."stages" for delete using (true);


-- Create sorties table
create table if not exists public.sorties (
    id bigint generated by default as identity primary key,
    created_at timestamp with time zone not null default now(),
    stage_id bigint not null references public.stages(id) on delete cascade,
    date date not null,
    title text not null,
    themes text[] not null,
    duration integer not null,
    summary text,
    content text not null
);
alter table "public"."sorties" enable row level security;
create policy "Enable read access for all users" on "public"."sorties" for select using (true);
create policy "Enable insert for authenticated users" on "public"."sorties" for insert with check (true);
create policy "Enable update for authenticated users" on "public"."sorties" for update using (true);
create policy "Enable delete for authenticated users" on "public"."sorties" for delete using (true);


-- Create etages table
create table if not exists public.etages (
    id text not null primary key, -- 'niveau', 'comprendre', 'observer', 'proteger'
    title text not null,
    icon text not null,
    color text not null,
    "order" smallint not null
);
alter table "public"."etages" enable row level security;
create policy "Enable read access for all users" on "public"."etages" for select using (true);
create policy "Enable CUD for authenticated users" on public.etages for all using (true);


-- Create options table
create table if not exists public.options (
    id text not null primary key,
    etage_id text not null references public.etages(id) on delete cascade,
    label text not null,
    duration integer,
    group_size text,
    activities text[],
    safety text[],
    tip text not null,
    materials text[],
    local_content text,
    "order" smallint not null
);
alter table "public"."options" enable row level security;
create policy "Enable read access for all users" on "public"."options" for select using (true);
create policy "Enable CUD for authenticated users" on public.options for all using (true);


-- Create content_cards table
create table if not exists public.content_cards (
    id text not null primary key,
    option_id text not null references public.options(id) on delete cascade,
    title text not null,
    description text not null,
    image text,
    "data-ai-hint" text,
    duration integer not null
);
alter table "public"."content_cards" enable row level security;
create policy "Enable read access for all users" on "public"."content_cards" for select using (true);
create policy "Enable CUD for authenticated users" on public.content_cards for all using (true);


-- Seed data
insert into public.etages (id, title, icon, color, "order") values
('niveau', 'Niveau', 'Target', 'from-blue-500 to-blue-600', 1),
('comprendre', 'Comprendre', 'BookOpen', 'from-yellow-500 to-yellow-600', 2),
('observer', 'Observer', 'Eye', 'from-purple-500 to-purple-600', 3),
('proteger', 'Protéger', 'Shield', 'from-green-500 to-green-600', 4)
on conflict (id) do nothing;

insert into public.options (id, etage_id, label, duration, group_size, activities, safety, tip, "order") values
('n1', 'niveau', 'N1 - Découverte', 45, '4-8 pers.', '{"Présentation environnement", "Observation guidée", "Jeu reconnaissance"}', '{"Gilets obligatoires", "Surveillance rapprochée"}', 'Éveil des sens et première approche de l''environnement marin', 1),
('n23', 'niveau', 'N2-N3 - Action consciente', 75, '6-10 pers.', '{"Analyse impact", "Gestes écoresponsables", "Mini-projet"}', '{"Autonomie surveillée", "Briefing sécurité"}', 'Passage à l''action avec compréhension des enjeux', 2),
('n45', 'niveau', 'N4-N5 - Expert responsable', 90, '8-12 pers.', '{"Planification autonome", "Encadrement pairs", "Projet collaboratif"}', '{"Co-responsabilité", "Debriefing approfondi"}', 'Leadership et transmission des bonnes pratiques', 3)
on conflict (id) do nothing;

insert into public.options (id, etage_id, label, materials, activities, local_content, tip, "order") values
('littoral', 'comprendre', 'Espace littoral', '{"Carte marine", "Jumelles"}', '{"Lecture paysage", "Identification zones", "Cartographie"}', 'Baie de Coutainville : zones protégées, chenaux, estran', 'Comprendre la géographie pour mieux naviguer et protéger', 1),
('biodiversite', 'comprendre', 'Biodiversité', '{"Guide espèces", "Appareil photo", "Carnet d''observation"}', '{"Inventaire faune", "Cycle de vie", "Périodes sensibles"}', 'Sternes, phoques veaux-marins, herbiers zostères', 'Identifier pour mieux respecter les espèces locales', 2),
('activites', 'comprendre', 'Activités humaines', '{"Schémas d''impact"}', '{"Analyse multi-usage", "Solutions durables", "Concertation"}', 'Pêche, conchyliculture, plaisance, kitesurf', 'Comprendre les interactions pour cohabiter harmonieusement', 3),
('sensoriel', 'observer', 'Approche sensorielle', '{"Silence requis", "Carnet sensations"}', '{"Écoute active", "Observation fine", "Ressenti corporel"}', 'Sons caractéristiques : clapot, cris oiseaux, vent dans gréement', 'Développer une connexion intime avec l''environnement', 1),
('reperes', 'observer', 'Repères navigation', '{"Carte marine", "Compas", "Jumelles"}', '{"Triangulation", "Lecture amers", "Estimation distances"}', 'Amers Coutainville : phare, clocher, château d''eau', 'Autonomie et sécurité par la lecture du paysage', 2),
('meteo', 'observer', 'Lecture météo', '{"Anémomètre", "Baromètre"}', '{"Analyse nuages", "Evolution vent", "Prévisions courtes"}', 'Vent dominant NO, grains fréquents, influence cotentin', 'Anticiper pour naviguer en sécurité', 3),
('impact', 'proteger', 'Réduire impact', '{"Ancres souples", "Sacs étanches"}', '{"Techniques mouillage", "Gestion déchets", "Navigation douce"}', 'Herbiers fragiles, zones de nidification, courants faibles', 'Chaque geste compte pour préserver l''écosystème', 1),
('pratiques', 'proteger', 'Éco-navigation', '{"Check-list verte", "Kit urgence écologique"}', '{"Trajectoires optimales", "Vitesses adaptées", "Comportement faune"}', 'Distances respect faune marine, zones interdites temporaires', 'Naviguer avec la nature, pas contre elle', 2),
('sciences', 'proteger', 'Sciences participatives', '{"Smartphone", "App iNaturalist", "Fiche observation"}', '{"Photos géolocalisées", "Saisie données", "Partage communauté"}', 'Réseau local observateurs, projets en cours', 'Contribuer à la connaissance scientifique', 3)
on conflict (id) do nothing;


insert into public.content_cards (id, option_id, title, duration, description, image, "data-ai-hint") values
('littoral-1', 'littoral', 'Les Laisses de mer', 10, 'Identifier ce que la mer dépose sur la plage et comprendre son rôle écologique.', 'https://placehold.co/600x400.png', 'beach seaweed'),
('littoral-2', 'littoral', 'La Dune', 15, 'Explorer la formation de la dune, sa végétation spécifique et son importance pour la protection du littoral.', 'https://placehold.co/600x400.png', 'sand dune'),
('littoral-3', 'littoral', 'Le Schorre et la Vasière', 10, 'Découvrir ces écosystèmes riches en biodiversité, véritables nurseries pour de nombreuses espèces.', 'https://placehold.co/600x400.png', 'salt marsh'),
('biodiv-1', 'biodiversite', 'Les Oiseaux marins', 10, 'Apprendre à reconnaître les espèces locales (Goélands, Sternes, Cormorans) et leurs comportements.', 'https://placehold.co/600x400.png', 'seabirds flying'),
('biodiv-2', 'biodiversite', 'Les Phoques de la baie', 5, 'Observer les phoques veaux-marins en respectant les distances de sécurité pour ne pas les déranger.', 'https://placehold.co/600x400.png', 'harbor seal'),
('biodiv-3', 'biodiversite', 'La Vie dans les rochers', 15, 'Partir à la recherche des bigorneaux, crabes et anémones à marée basse et apprendre leur rôle.', 'https://placehold.co/600x400.png', 'tide pool'),
('biodiv-4', 'biodiversite', 'Le Plancton, poumon de l''océan', 5, 'Comprendre le rôle vital du plancton dans la production d''oxygène et la chaîne alimentaire marine.', 'https://placehold.co/600x400.png', 'plankton microscope'),
('activites-1', 'activites', 'La Pêche à pied', 10, 'Découvrir les bonnes pratiques pour une pêche respectueuse de la ressource (tailles, outils, saisons).', 'https://placehold.co/600x400.png', 'low tide'),
('activites-2', 'activites', 'La Conchyliculture', 5, 'Comprendre l''élevage des huîtres et des moules sur les bouchots et son impact sur l''écosystème.', 'https://placehold.co/600x400.png', 'oyster farm'),
('activites-3', 'activites', 'La Plaisance et les Sports Nautiques', 10, 'Analyser l''impact des ancres, des carénages et du bruit sur le milieu marin.', 'https://placehold.co/600x400.png', 'sailing boat'),
('sensoriel-1', 'sensoriel', 'Palette de couleurs', 5, 'Observer et nommer les différentes couleurs de l''eau, du ciel et du sable selon la météo et l''heure.', 'https://placehold.co/600x400.png', 'ocean colors'),
('sensoriel-2', 'sensoriel', 'Concert marin', 10, 'Fermer les yeux et identifier les différents sons : le clapotis, le vent, les oiseaux, le ressac.', 'https://placehold.co/600x400.png', 'ocean waves'),
('sensoriel-3', 'sensoriel', 'Senteurs du littoral', 5, 'Identifier les odeurs caractéristiques de la mer : l''iode, le sable chaud, les algues.', 'https://placehold.co/600x400.png', 'beach sunset'),
('reperes-1', 'reperes', 'Identifier les amers', 10, 'Apprendre à reconnaître et utiliser les points de repère fixes sur la côte (phare, église, château d''eau).', 'https://placehold.co/600x400.png', 'lighthouse coast'),
('reperes-2', 'reperes', 'Lire une carte marine', 15, 'Se familiariser avec les symboles, les sondes de profondeur et les dangers indiqués sur une carte marine locale.', 'https://placehold.co/600x400.png', 'nautical chart'),
('reperes-3', 'reperes', 'S''orienter avec le soleil', 5, 'Utiliser la position du soleil pour déterminer les points cardinaux et estimer l''heure.', 'https://placehold.co/600x400.png', 'sun compass'),
('meteo-1', 'meteo', 'Les Familles de nuages', 10, 'Apprendre à reconnaître les cirrus, cumulus et stratus et ce qu''ils indiquent sur le temps à venir.', 'https://placehold.co/600x400.png', 'cloud types'),
('meteo-2', 'meteo', 'Lire la direction du vent', 5, 'Observer les vagues, les drapeaux ou les fumées pour déterminer d''où vient le vent.', 'https://placehold.co/600x400.png', 'wind direction'),
('meteo-3', 'meteo', 'L''Échelle de Beaufort', 10, 'Estimer la force du vent en observant l''état de la mer, de la petite brise à la tempête.', 'https://placehold.co/600x400.png', 'stormy sea'),
('impact-1', 'impact', 'Le Défi Zéro Déchet', 5, 'Adopter la règle des 3R (Réduire, Réutiliser, Recycler) pendant une sortie en mer.', 'https://placehold.co/600x400.png', 'ocean plastic'),
('impact-2', 'impact', 'Mouillage écologique', 10, 'Apprendre à jeter l''ancre sans endommager les fonds marins, en particulier les herbiers.', 'https://placehold.co/600x400.png', 'boat anchor'),
('impact-3', 'impact', 'Nettoyage de plage', 15, 'Organiser une mini-collecte de déchets sur la plage en identifiant leur provenance.', 'https://placehold.co/600x400.png', 'beach cleanup'),
('pratiques-1', 'pratiques', 'Approcher la faune', 10, 'Apprendre les bonnes distances et les angles d''approche pour observer les animaux sans les stresser.', 'https://placehold.co/600x400.png', 'dolphins boat'),
('pratiques-2', 'pratiques', 'Entretien du bateau', 5, 'Choisir des produits de nettoyage écologiques et des antifoulings non toxiques.', 'https://placehold.co/600x400.png', 'boat cleaning'),
('pratiques-3', 'pratiques', 'Gérer les eaux usées', 5, 'Comprendre pourquoi il ne faut jamais vidanger les réservoirs d''eaux noires ou grises près des côtes.', 'https://placehold.co/600x400.png', 'clean water'),
('sciences-1', 'sciences', 'BioLit : la biodiversité du littoral', 15, 'Participer au programme BioLit en photographiant des espèces spécifiques sur l''estran.', 'https://placehold.co/600x400.png', 'citizen science'),
('sciences-2', 'sciences', 'Plages Vivantes', 15, 'Suivre le protocole de Plages Vivantes pour évaluer la santé écologique du haut de la plage.', 'https://placehold.co/600x400.png', 'sandy beach'),
('sciences-3', 'sciences', 'Signaler une pollution', 5, 'Apprendre à identifier et à signaler une pollution suspecte aux autorités compétentes.', 'https://placehold.co/600x400.png', 'oil spill')
on conflict (id) do nothing;
