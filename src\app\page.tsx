
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ArrowRight } from 'lucide-react';

export default function HomePage() {
  const router = useRouter();
  const [username, setUsername] = useState('');

  useEffect(() => {
    const storedUsername = localStorage.getItem('econav_username');
    if (storedUsername) {
      setUsername(storedUsername);
    } else {
      setUsername('Moniteur_Demo');
    }
  }, []);

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (username) {
      localStorage.setItem('econav_username', username);
      router.push('/stages');
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-secondary p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center space-y-4 pt-8">
          <div className="flex items-center justify-center">
             <Image src="/assets/logoCopun1.png" alt="Logo Cop'un de la mer" width={192} height={119} className="w-48 h-auto" />
          </div>
          <div className="space-y-1">
            <h2 className="text-xl font-semibold">Bienvenue !</h2>
            <p className="text-muted-foreground text-sm">
                Votre outil pédagogique pour les sports de plein air.
            </p>
          </div>
        </CardHeader>
        <CardContent className="p-8 pt-4">
            <p className="text-center text-muted-foreground text-sm mb-6">
                Conçu pour les moniteurs, cet outil vous aide à créer des fiches de sortie personnalisées en fonction des conditions météo, du public et des objectifs pédagogiques.
            </p>
            <form onSubmit={handleLogin} className="space-y-6">
                <div className="space-y-2">
                    <Label htmlFor="username">Nom d'utilisateur</Label>
                    <Input
                        id="username"
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        required
                    />
                </div>
                <Button type="submit" size="lg" className="w-full text-base py-6">
                    <ArrowRight className="mr-2 h-5 w-5" />
                    Se connecter
                </Button>
            </form>
            <Separator className="my-6" />
            <div className="text-center space-y-3">
                <p className="text-xs text-muted-foreground font-semibold uppercase tracking-wider">Partenaires</p>
                <div className="flex justify-center">
                    <Image src="/assets/ffv_logo.png" alt="Logo FFVoile" width={100} height={28} className="h-10 w-auto" />
                </div>
            </div>
        </CardContent>
      </Card>
    </div>
  );
}
