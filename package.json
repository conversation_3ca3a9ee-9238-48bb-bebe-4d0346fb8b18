{"name": "copun-de-la-mer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@capacitor/android": "^7.4.3", "@capacitor/camera": "^7.0.2", "@capacitor/core": "^7.4.3", "@capacitor/ios": "^7.4.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.45.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "framer-motion": "^11.3.19", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.475.0", "mapbox-gl": "^3.5.2", "next": "15.3.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-map-gl": "^7.1.7", "recharts": "^2.15.1", "swiper": "^11.1.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zod": "^3.24.2"}, "devDependencies": {"@types/mapbox-gl": "^3.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/recharts": "^1.8.29", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}