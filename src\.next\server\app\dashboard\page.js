(()=>{var e={};e.id=105,e.ids=[105],e.modules={2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,r)=>{"use strict";var s=r(96575);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},11260:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(28625);r(64996);var n=r(11107),o=r(48382);function a(){return(0,n.useRouter)(),(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-[calc(100vh-200px)]",children:[(0,s.jsx)(o.A,{className:"w-8 h-8 animate-spin text-primary"}),(0,s.jsx)("p",{className:"ml-4 text-muted-foreground",children:"Redirection..."})]})}},12300:(e,t,r)=>{Promise.resolve().then(r.bind(r,11260))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25452:(e,t,r)=>{Promise.resolve().then(r.bind(r,49874))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29492:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(64996);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:i="",children:l,iconNode:d,...c},p)=>(0,s.createElement)("svg",{ref:p,...a,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:o("lucide",i),...c},[...d.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...a},l)=>(0,s.createElement)(i,{ref:l,iconNode:t,className:o(`lucide-${n(e)}`,r),...a}));return r.displayName=`${e}`,r}},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(41808);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},48382:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(29492).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},49874:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\dashboard\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77190:()=>{},79551:e=>{"use strict";e.exports=require("url")},87830:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(5853),n=r(60554),o=r(30708),a=r.n(o),i=r(8067),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49874)),"C:\\PL\\COPUN-V5\\src\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PL\\COPUN-V5\\src\\app\\dashboard\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let n={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function o({children:e}){return(0,s.jsxs)("html",{lang:"fr",children:[(0,s.jsxs)("head",{children:[(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,s.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,s.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,s.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,s.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,s.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,s.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,s.jsxs)("body",{className:"font-body antialiased",children:[(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,s.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[683,425,808],()=>r(87830));module.exports=s})();