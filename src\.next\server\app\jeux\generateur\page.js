(()=>{var e={};e.id=520,e.ids=[520],e.modules={2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,r)=>{"use strict";var n=r(96575);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26324:(e,t,r)=>{Promise.resolve().then(r.bind(r,59015))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},34744:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>c});var n=r(5853),o=r(60554),s=r(30708),i=r.n(s),a=r(8067),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let c={children:["",{children:["jeux",{children:["generateur",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59015)),"C:\\PL\\COPUN-V5\\src\\app\\jeux\\generateur\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\PL\\COPUN-V5\\src\\app\\jeux\\generateur\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/jeux/generateur/page",pathname:"/jeux/generateur",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},45950:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("WandSparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]])},59015:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\jeux\\\\generateur\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\jeux\\generateur\\page.tsx","default")},61825:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},62708:(e,t,r)=>{Promise.resolve().then(r.bind(r,68538))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68538:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(28625),o=r(64996),s=r(11107),i=r(48382),a=r(61825),d=r(45950);let c=(0,r(29492).A)("Dices",[["rect",{width:"12",height:"12",x:"2",y:"10",rx:"2",ry:"2",key:"6agr2n"}],["path",{d:"m17.92 14 3.5-3.5a2.24 2.24 0 0 0 0-3l-5-4.92a2.24 2.24 0 0 0-3 0L10 6",key:"1o487t"}],["path",{d:"M6 18h.01",key:"uhywen"}],["path",{d:"M10 14h.01",key:"ssrbsk"}],["path",{d:"M15 6h.01",key:"cblpky"}],["path",{d:"M18 9h.01",key:"2061c0"}]]);var l=r(93948),u=r.n(l);r(87571),r(19936),function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/checkbox'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/slider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}();let m=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).flatMap(e=>e.themes),p=[{id:"triage",label:"Vrai ou Faux"},{id:"mots",label:"Mots en Rafale"},{id:"dilemme",label:"Dilemmes"},{id:"quizz",label:"Quizz"}];function h(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{toast:r}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[l,h]=(0,o.useTransition)(),f=t.get("stageId")?parseInt(t.get("stageId"),10):null,[O,x]=(0,o.useState)(null),[v,j]=(0,o.useState)(""),[b,N]=(0,o.useState)([]),[_,E]=(0,o.useState)(!0),[U,g]=(0,o.useState)(["triage","mots","dilemme","quizz"]),[D,w]=(0,o.useState)([10]),C=e=>{g(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},y=e=>{N(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},M=(0,o.useMemo)(()=>Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>({...e,themes:e.themes.map(e=>({...e,selected:b.includes(e.id)}))})),[b]),L=f?`/stages/${f}`:"/jeux";return _?(0,n.jsxs)("div",{className:"flex flex-col gap-4 justify-center items-center h-64",children:[(0,n.jsx)(i.A,{className:"w-8 h-8 animate-spin text-primary"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Chargement du g\xe9n\xe9rateur..."})]}):(0,n.jsxs)("div",{className:"max-w-2xl mx-auto space-y-8",children:[(0,n.jsxs)(u(),{href:L,className:"text-sm text-muted-foreground hover:text-foreground flex items-center gap-1",children:[(0,n.jsx)(a.A,{className:"w-4 h-4"}),f?"Retour au stage":`Retour \xe0 la biblioth\xe8que`]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("h1",{className:"text-3xl font-bold font-headline flex items-center gap-3",children:[(0,n.jsx)(d.A,{className:"w-8 h-8 text-primary"})," G\xe9n\xe9rateur de Jeu Assist\xe9"]}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Configurez votre jeu id\xe9al et laissez l'assistant le composer pour vous en quelques secondes."})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"1. Informations sur le jeu"})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"title",children:"Titre du jeu"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"title",value:v,onChange:e=>j(e.target.value),placeholder:"Titre du jeu"})]})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"2. Th\xe8mes du jeu"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm text-muted-foreground pt-2",children:[f?"Les th\xe8mes du stage sont pr\xe9-s\xe9lectionn\xe9s. ":"","Cliquez sur les badges pour personnaliser la s\xe9lection."]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:M.map(e=>(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-muted-foreground mb-2",children:e.label}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:e.themes.map(e=>{let t=e.icon;return(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:e.selected?"default":"secondary",onClick:()=>y(e.id),className:"text-base cursor-pointer px-3 py-1",children:[(0,n.jsx)(t,{className:"w-4 h-4 mr-2"}),e.title]},e.id)})})]},e.label))})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"3. Contenu du jeu"})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"font-medium",children:"Types de questions \xe0 inclure"}),(0,n.jsx)("div",{className:"grid grid-cols-2 gap-4 mt-2",children:p.map(e=>(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/checkbox'");throw e.code="MODULE_NOT_FOUND",e}()),{id:e.id,checked:U.includes(e.id),onCheckedChange:()=>C(e.id)}),(0,n.jsx)("label",{htmlFor:e.id,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e.label})]},e.id))})]}),(0,n.jsxs)("div",{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"font-medium",children:["Nombre de questions (",D[0],")"]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/slider'");throw e.code="MODULE_NOT_FOUND",e}()),{value:D,onValueChange:w,min:5,max:25,step:1,className:"mt-3"})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>{h(async()=>{if(0===U.length)return void r({title:"S\xe9lection requise",description:"Veuillez choisir au moins un type de question.",variant:"destructive"});let t=b.map(e=>m.find(t=>t.id===e)?.title).filter(Boolean),n=await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(U,t);if(0===n.length)return void r({title:"Aucune carte trouv\xe9e",description:"Impossible de trouver des cartes avec les filtres actuels. Essayez d'\xe9largir votre recherche.",variant:"destructive"});let o=n.sort(()=>.5-Math.random()).slice(0,D[0]),s=o.filter(e=>"triage"===e.type).map(e=>({id:e.id,statement:e.statement,isTrue:e.isTrue,theme:e.theme,related_objective_id:e.related_objective_id})),i=o.filter(e=>"mots"===e.type).map(e=>({id:e.id,definition:e.definition,answer:e.answer,theme:e.theme,related_objective_id:e.related_objective_id})),a=o.filter(e=>"dilemme"===e.type).map(e=>({id:e.id,optionA:e.optionA,optionB:e.optionB,explanation:e.explanation,theme:e.theme,related_objective_id:e.related_objective_id})),d=o.filter(e=>"quizz"===e.type).map(e=>({id:e.id,question:e.question,answers:e.answers,correctAnswerIndex:e.correctAnswerIndex,theme:e.theme,related_objective_id:e.related_objective_id})),c=t.join(", ")||"Th\xe8mes vari\xe9s",l=await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(v,c,{triageCôtier:{title:"Le Triage C\xf4tier",instruction:"D\xe9m\xeale le vrai du faux dans les affirmations suivantes.",items:s},motsEnRafale:{title:"Les Mots en Rafale",instruction:"Retrouve les mots qui correspondent \xe0 ces d\xe9finitions !",items:i},dilemmeDuMarin:{title:"Le Dilemme du Marin",instruction:"Que pr\xe9f\xe8res-tu ? Il n'y a pas de mauvaise r\xe9ponse, mais chaque choix a une cons\xe9quence !",items:a},leGrandQuizz:{title:"Le Grand Quizz",instruction:"Une seule bonne r\xe9ponse par question !",items:d}},f);l?(r({title:"Jeu cr\xe9\xe9 avec succ\xe8s !",description:"Le jeu a \xe9t\xe9 g\xe9n\xe9r\xe9 et ajout\xe9 \xe0 votre biblioth\xe8que."}),e.push(`/jeux/${l.id}`)):r({title:"Erreur de cr\xe9ation",description:"Une erreur est survenue. Veuillez r\xe9essayer.",variant:"destructive"})})},disabled:l,className:"w-full text-lg py-6",children:[l?(0,n.jsx)(i.A,{className:"mr-2 h-5 w-5 animate-spin"}):(0,n.jsx)(c,{className:"mr-2 h-5 w-5"}),l?"G\xe9n\xe9ration en cours...":`G\xe9n\xe9rer le jeu`]})]})}},77190:()=>{},79551:e=>{"use strict";e.exports=require("url")},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>o});var n=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function s({children:e}){return(0,n.jsxs)("html",{lang:"fr",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,n.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,n.jsxs)("body",{className:"font-body antialiased",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,425,808,97,410],()=>r(34744));module.exports=n})();