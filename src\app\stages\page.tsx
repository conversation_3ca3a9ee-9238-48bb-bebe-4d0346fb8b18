

'use client';

import React, { useState, useEffect, useMemo, useTransition } from 'react';
import Link from 'next/link';
import { Loader2, Calendar, Users, ChevronDown, CheckCircle2, BookOpen, Eye, Shield, Trophy } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { CreateStageForm } from '@/components/create-stage-form';
import { format, parseISO, isBefore, isAfter, startOfToday, isSameDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { Stage, GrandTheme, AssignedMission, Mission } from '@/lib/types';
import { getStages, createStage as serverCreateStage, getSortiesForStage } from '@/app/actions';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { jsonQuestions, groupedThemes } from '@/data/etages';
import { allMissions } from '@/data/missions';

type ThemeProgress = {
    [themeTitle: string]: { completed: number, total: number };
};

interface MissionProgress {
    completed: number;
    total: number;
}

interface StageWithProgress extends Stage {
    objectivesProgress: ThemeProgress;
    missionsProgress: MissionProgress;
    mainThemes: GrandTheme[];
}

const allThemes = groupedThemes.flatMap(g => g.themes);


const StageCard = ({ stage }: { stage: StageWithProgress }) => {
    const today = startOfToday();
    const startDate = parseISO(stage.start_date);
    const endDate = parseISO(stage.end_date);

    let status: 'À venir' | 'En cours' | 'Passé';
    if (isBefore(endDate, today)) {
        status = 'Passé';
    } else if (isAfter(startDate, today)) {
        status = 'À venir';
    } else {
        status = 'En cours';
    }

    const statusColors = {
        'À venir': 'bg-yellow-100 text-yellow-800 border-yellow-200',
        'En cours': 'bg-blue-100 text-blue-800 border-blue-200',
        'Passé': 'bg-gray-100 text-gray-700 border-gray-200',
    }
    
    const [mainTitle, ...subtitles] = stage.title.split(' - ');
    const subtitle = subtitles.join(' - ');

    const objectivesPercentage = useMemo(() => {
        const totals = Object.values(stage.objectivesProgress).reduce((acc, p) => {
            acc.completed += p.completed;
            acc.total += p.total;
            return acc;
        }, { completed: 0, total: 0 });
        return totals.total > 0 ? (totals.completed / totals.total) * 100 : 0;
    }, [stage.objectivesProgress]);

    const missionsPercentage = useMemo(() => {
        const { completed, total } = stage.missionsProgress;
        return total > 0 ? (completed / total) * 100 : 0;
    }, [stage.missionsProgress]);

    return (
        <Link href={`/stages/${stage.id}`} className="block group">
            <Card className="hover:shadow-lg transition-shadow h-full flex flex-col">
                <CardHeader>
                    <div className="flex justify-between items-start gap-2">
                        <CardTitle className="text-lg font-bold group-hover:text-primary transition-colors">
                            {mainTitle}
                        </CardTitle>
                        <Badge variant="outline" className={cn(statusColors[status], 'shrink-0')}>{status}</Badge>
                    </div>
                    <CardDescription className="flex items-center flex-wrap text-xs">
                        {subtitle ? (
                            <>
                                <span className="font-medium">{subtitle}</span>
                                <span className="mx-1.5">&middot;</span>
                                <span>{stage.type}</span>
                            </>
                        ) : (
                            stage.type
                        )}
                    </CardDescription>
                </CardHeader>
                <CardContent className="flex-grow space-y-4">
                    <div className="space-y-2 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            <span>{format(startDate, "d MMM", { locale: fr })} - {format(endDate, "d MMM yyyy", { locale: fr })}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <Users className="w-4 h-4" />
                            <span>{stage.participants} participants</span>
                        </div>
                    </div>
                     <div className="space-y-3 pt-2">
                        <div>
                            <div className="flex justify-between items-center text-xs text-muted-foreground mb-1">
                                <span className='flex items-center gap-1.5'><BookOpen className="w-3.5 h-3.5" /> Objectifs</span>
                                <span>{Math.round(objectivesPercentage)}%</span>
                            </div>
                            <Progress value={objectivesPercentage} className="h-1.5"/>
                        </div>
                        <div>
                             <div className="flex justify-between items-center text-xs text-muted-foreground mb-1">
                                <span className='flex items-center gap-1.5'><Trophy className="w-3.5 h-3.5" /> Missions</span>
                                <span>{stage.missionsProgress.completed} / {stage.missionsProgress.total}</span>
                            </div>
                            <Progress value={missionsPercentage} className="h-1.5"/>
                        </div>
                    </div>
                </CardContent>
                 <CardFooter className="flex-col items-start gap-3 border-t pt-4">
                    <Accordion type="single" collapsible className="w-full">
                        <AccordionItem value="details" className="border-b-0">
                            <AccordionTrigger
                                onClick={(e) => e.preventDefault()}
                                className="text-sm font-semibold text-foreground hover:no-underline py-0"
                            >
                                Voir le détail de la progression
                            </AccordionTrigger>
                            <AccordionContent className="pt-4 space-y-2">
                                {Object.keys(stage.objectivesProgress).length > 0 ? (
                                    Object.entries(stage.objectivesProgress).map(([themeTitle, p]) => {
                                         const theme = stage.mainThemes.find(t => t.title === themeTitle);
                                         if (!theme) return null;
                                         const ThemeIcon = theme.icon;
                                         const percentage = p.total > 0 ? (p.completed / p.total) * 100 : 0;
                                         if(p.total === 0) return null;

                                         return (
                                             <div key={theme.id}>
                                                 <div className="flex justify-between items-center text-xs text-muted-foreground mb-1">
                                                     <span className='flex items-center gap-1.5'><ThemeIcon className="w-3.5 h-3.5" /> {theme.title}</span>
                                                     <span>{p.completed} / {p.total}</span>
                                                 </div>
                                                 <Progress value={percentage} className="h-1.5"/>
                                             </div>
                                         )
                                    })
                                ) : (
                                    <p className="text-xs text-muted-foreground text-center py-2">Aucun programme d'objectifs défini.</p>
                                )}
                            </AccordionContent>
                        </AccordionItem>
                    </Accordion>
                </CardFooter>
            </Card>
        </Link>
    );
};


export default function StageManagerPage() {
    const [stagesWithProgress, setStagesWithProgress] = useState<StageWithProgress[]>([]);
    const [loading, setLoading] = useState(true);
    const [isCreating, startCreateTransition] = useTransition();
    const { toast } = useToast();

    const fetchStages = async () => {
        setLoading(true);
        const stagesData = await getStages();
        
        const stagesWithProgressData: StageWithProgress[] = await Promise.all(
            stagesData.map(async (stage) => {
                const sorties = await getSortiesForStage(stage.id);
                const programSortie = sorties.find(s => s.selected_content?.program?.length ?? 0 > 0);

                const objectivesProgress: ThemeProgress = {};
                let mainThemes: GrandTheme[] = [];
                let missionsProgress: MissionProgress = { completed: 0, total: 0 };
                
                // Calculate objectives progress
                if (programSortie && programSortie.selected_content.program) {
                    const programObjectiveIds = new Set(programSortie.selected_content.program);
                    
                    const completedFromStorage = localStorage.getItem(`completed_objectives_${stage.id}`);
                    const completedIds = completedFromStorage ? new Set(JSON.parse(completedFromStorage)) : new Set();
                    
                    const allThemeIdsInProgram = new Set<string>();

                    programObjectiveIds.forEach(objId => {
                        const question = jsonQuestions.questions_copun.find(q => `q${q.id}` === objId);
                        if(question) {
                            question.tags_theme.forEach(themeId => allThemeIdsInProgram.add(themeId));
                        }
                    });

                    mainThemes = Array.from(allThemeIdsInProgram).map(themeId => allThemes.find(t => t.id === themeId)).filter((t): t is GrandTheme => !!t);
                    
                    mainThemes.forEach(theme => {
                        objectivesProgress[theme.title] = { completed: 0, total: 0 };
                    });

                    programObjectiveIds.forEach(objId => {
                        const question = jsonQuestions.questions_copun.find(q => `q${q.id}` === objId);
                        if(question) {
                            mainThemes.forEach(theme => {
                                if (question.tags_theme.includes(theme.id)) {
                                    if (!objectivesProgress[theme.title]) {
                                        objectivesProgress[theme.title] = { completed: 0, total: 0 };
                                    }
                                    objectivesProgress[theme.title].total++;
                                    if (completedIds.has(objId)) {
                                        objectivesProgress[theme.title].completed++;
                                    }
                                }
                            });
                        }
                    });
                }
                
                // Calculate missions progress
                const storedAssignedMissions = localStorage.getItem(`assigned_missions_${stage.id}`);
                const assignedMissions: AssignedMission[] = storedAssignedMissions ? JSON.parse(storedAssignedMissions) : [];
                missionsProgress.total = assignedMissions.length;
                missionsProgress.completed = assignedMissions.filter(m => m.status === 'complete').length;


                return {
                    ...stage,
                    objectivesProgress,
                    missionsProgress,
                    mainThemes,
                };
            })
        );

        setStagesWithProgress(stagesWithProgressData);
        setLoading(false);
    };

    useEffect(() => {
        fetchStages();
    }, []);

    const { currentStages, upcomingStages, pastStages } = useMemo(() => {
        const today = startOfToday();
        const sortedStages = stagesWithProgress.sort((a,b) => parseISO(a.start_date).getTime() - parseISO(b.start_date).getTime());
        
        return sortedStages.reduce(
            (acc, stage) => {
                const startDate = parseISO(stage.start_date);
                const endDate = parseISO(stage.end_date);
                 if (isBefore(endDate, today)) {
                    acc.pastStages.push(stage);
                } else if (isAfter(startDate, today)) {
                    acc.upcomingStages.push(stage);
                } else {
                    acc.currentStages.push(stage);
                }
                return acc;
            },
            { currentStages: [] as StageWithProgress[], upcomingStages: [] as StageWithProgress[], pastStages: [] as StageWithProgress[] }
        );
    }, [stagesWithProgress]);


    const handleStageCreate = (data: Omit<Stage, 'id' | 'created_at'>) => {
        startCreateTransition(async () => {
            const created = await serverCreateStage(data);
            if (created) {
                await fetchStages(); 
                toast({
                    title: "Stage créé",
                    description: "Le nouveau stage a été ajouté.",
                });
            } else {
                toast({
                    title: "Erreur",
                    description: "La création du stage a échoué.",
                    variant: "destructive"
                });
            }
        });
    };
    
    return (
        <div className="space-y-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-foreground font-headline">Mes Stages</h1>
                    <p className="text-muted-foreground">Planifiez, suivez et gérez tous vos stages.</p>
                </div>
                <CreateStageForm onStageCreate={handleStageCreate} isCreating={isCreating} />
            </div>

            {loading ? (
                <div className="p-10 text-center text-muted-foreground">
                    <Loader2 className="mx-auto h-8 w-8 animate-spin mb-4" />
                    <p>Chargement des stages...</p>
                </div>
            ) : stagesWithProgress.length > 0 ? (
                <div className="space-y-8">
                    {currentStages.length > 0 && (
                        <section>
                            <h2 className="text-2xl font-semibold mb-4 text-foreground">En cours</h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {currentStages.map(stage => <StageCard key={stage.id} stage={stage} />)}
                            </div>
                        </section>
                    )}

                    {upcomingStages.length > 0 && (
                        <section>
                             <h2 className="text-2xl font-semibold mb-4 text-foreground">À venir</h2>
                             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {upcomingStages.map(stage => <StageCard key={stage.id} stage={stage} />)}
                            </div>
                        </section>
                    )}

                    {pastStages.length > 0 && (
                        <section>
                             <Accordion type="single" collapsible className="w-full">
                                <AccordionItem value="past-stages" className="border-b-0">
                                    <AccordionTrigger className="text-xl font-semibold hover:no-underline text-muted-foreground">
                                        Stages passés ({pastStages.length})
                                    </AccordionTrigger>
                                    <AccordionContent>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 pt-4">
                                            {pastStages.sort((a,b) => parseISO(b.start_date).getTime() - parseISO(a.start_date).getTime()).map(stage => (
                                                <StageCard key={stage.id} stage={stage} />
                                            ))}
                                        </div>
                                    </AccordionContent>
                                </AccordionItem>
                            </Accordion>
                        </section>
                    )}
                </div>
            ) : (
                <Card>
                    <CardContent className="p-10 text-center text-muted-foreground">
                        <p className="text-lg mb-2">Aucun stage planifié.</p>
                        <p>Utilisez le bouton "Nouveau Stage" pour commencer.</p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
