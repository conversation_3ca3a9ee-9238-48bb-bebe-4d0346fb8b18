(()=>{var e={};e.id=974,e.ids=[974],e.modules={2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3728:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>u});var n=r(5853),o=r(60554),i=r(30708),s=r.n(i),a=r(8067),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10257)),"C:\\PL\\COPUN-V5\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PL\\COPUN-V5\\src\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},7055:()=>{},10257:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,r)=>{"use strict";var n=r(96575);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21159:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),r(17646);let n=r(63110),o=r(44890),i=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var r,l;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:h=!1,loading:g,className:v,quality:b,width:x,height:_,fill:y=!1,style:O,overrideSrc:j,onLoad:w,onLoadingComplete:P,placeholder:C="empty",blurDataURL:E,fetchPriority:N,decoding:S="async",layout:M,objectFit:D,objectPosition:U,lazyBoundary:R,lazyRoot:k,...z}=e,{imgConf:L,showAltText:A,blurComplete:T,defaultLoader:I}=t,F=L||o.imageConfigDefault;if("allSizes"in F)u=F;else{let e=[...F.deviceSizes,...F.imageSizes].sort((e,t)=>e-t),t=F.deviceSizes.sort((e,t)=>e-t),n=null==(r=F.qualities)?void 0:r.sort((e,t)=>e-t);u={...F,allSizes:e,deviceSizes:t,qualities:n}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let q=z.loader||I;delete z.loader,delete z.srcSet;let G="__next_img_default"in q;if(G){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=q;q=t=>{let{config:r,...n}=t;return e(n)}}if(M){"fill"===M&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(O={...O,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let V="",B=a(x),W=a(_);if((l=f)&&"object"==typeof l&&(s(l)||void 0!==l.src)){let e=s(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,E=E||e.blurDataURL,V=e.src,!y)if(B||W){if(B&&!W){let t=B/e.width;W=Math.round(e.height*t)}else if(!B&&W){let t=W/e.height;B=Math.round(e.width*t)}}else B=e.width,W=e.height}let X=!h&&("lazy"===g||void 0===g);(!(f="string"==typeof f?f:V)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,X=!1),u.unoptimized&&(m=!0),G&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let $=a(b),H=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:D,objectPosition:U}:{},A?{}:{color:"transparent"},O),J=T||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:B,heightInt:W,blurWidth:d,blurHeight:c,blurDataURL:E||"",objectFit:H.objectFit})+'")':'url("'+C+'")',Y=i.includes(H.objectFit)?"fill"===H.objectFit?"100% 100%":"cover":H.objectFit,Z=J?{backgroundSize:Y,backgroundPosition:H.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},K=function(e){let{config:t,src:r,unoptimized:n,width:o,quality:i,sizes:s,loader:a}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:o}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:o.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:o,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>o.find(t=>t>=e)||o[o.length-1]))],kind:"x"}}(t,o,s),d=l.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:l.map((e,n)=>a({config:t,src:r,quality:i,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:a({config:t,src:r,quality:i,width:l[d]})}}({config:u,src:f,unoptimized:m,width:B,quality:$,sizes:p,loader:q});return{props:{...z,loading:X?"lazy":g,fetchPriority:N,width:B,height:W,decoding:S,className:v,style:{...H,...Z},sizes:K.sizes,srcSet:K.srcSet,src:j||K.src},meta:{unoptimized:m,priority:h,placeholder:C,fill:y}}}},22223:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let n=r(64819),o=r(21159),i=r(50199),s=n._(r(76239));function a(e){let{props:t}=(0,o.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=i.Image},24870:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},25437:(e,t,r)=>{"use strict";e.exports=r(53339).vendored.contexts.AmpContext},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29492:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var n=r(64996);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:l,iconNode:u,...d},c)=>(0,n.createElement)("svg",{ref:c,...s,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:i("lucide",a),...d},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]])),l=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...s},l)=>(0,n.createElement)(a,{ref:l,iconNode:t,className:i(`lucide-${o(e)}`,r),...s}));return r.displayName=`${e}`,r}},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},35011:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(28625),o=r(64996),i=r(11107),s=r(22223),a=r.n(s),l=r(24870);function u(){let e=(0,i.useRouter)(),[t,r]=(0,o.useState)("");return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-secondary p-4",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-md shadow-lg",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center space-y-4 pt-8",children:[(0,n.jsx)("div",{className:"flex items-center justify-center",children:(0,n.jsx)(a(),{src:"/assets/logoCopun1.png",alt:"Logo Cop'un de la mer",width:192,height:119,className:"w-48 h-auto"})}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold",children:"Bienvenue !"}),(0,n.jsx)("p",{className:"text-muted-foreground text-sm",children:"Votre outil p\xe9dagogique pour les sports de plein air."})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-8 pt-4",children:[(0,n.jsx)("p",{className:"text-center text-muted-foreground text-sm mb-6",children:"Con\xe7u pour les moniteurs, cet outil vous aide \xe0 cr\xe9er des fiches de sortie personnalis\xe9es en fonction des conditions m\xe9t\xe9o, du public et des objectifs p\xe9dagogiques."}),(0,n.jsxs)("form",{onSubmit:r=>{r.preventDefault(),t&&(localStorage.setItem("econav_username",t),e.push("/stages"))},className:"space-y-6",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"username",children:"Nom d'utilisateur"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"username",type:"text",value:t,onChange:e=>r(e.target.value),required:!0})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"submit",size:"lg",className:"w-full text-base py-6",children:[(0,n.jsx)(l.A,{className:"mr-2 h-5 w-5"}),"Se connecter"]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/separator'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"my-6"}),(0,n.jsxs)("div",{className:"text-center space-y-3",children:[(0,n.jsx)("p",{className:"text-xs text-muted-foreground font-semibold uppercase tracking-wider",children:"Partenaires"}),(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(a(),{src:"/assets/ffv_logo.png",alt:"Logo FFVoile",width:100,height:28,className:"h-10 w-auto"})})]})]})]})})}!function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/separator'");throw e.code="MODULE_NOT_FOUND",e}()},39222:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return c}});let n=r(64819),o=r(97834),i=r(28625),s=o._(r(64996)),a=n._(r(88429)),l=r(25437),u=r(56527),d=r(72878);function c(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(17646);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let i=!0,s=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){s=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?i=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!s)&&r.has(e)?i=!1:(r.add(e),n[t]=r)}}}return i}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:n})})}let h=function(e){let{children:t}=e,r=(0,s.useContext)(l.AmpStateContext),n=(0,s.useContext)(u.HeadManagerContext);return(0,i.jsx)(a.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44890:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},50199:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let n=r(64819),o=r(97834),i=r(28625),s=o._(r(64996)),a=n._(r(58709)),l=n._(r(39222)),u=r(21159),d=r(44890),c=r(80433);r(17646);let f=r(69922),p=n._(r(76239)),m=r(96304),h={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function g(e,t,r,n,o,i,s){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&o(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,o=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>o,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{o=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function v(e){return s.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,s.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:o,height:a,width:l,decoding:u,className:d,style:c,fetchPriority:f,placeholder:p,loading:h,unoptimized:b,fill:x,onLoadRef:_,onLoadingCompleteRef:y,setBlurComplete:O,setShowAltText:j,sizesInput:w,onLoad:P,onError:C,...E}=e,N=(0,s.useCallback)(e=>{e&&(C&&(e.src=e.src),e.complete&&g(e,p,_,y,O,b,w))},[r,p,_,y,O,C,b,w]),S=(0,m.useMergedRef)(t,N);return(0,i.jsx)("img",{...E,...v(f),loading:h,width:l,height:a,decoding:u,"data-nimg":x?"fill":"1",className:d,style:c,sizes:o,srcSet:n,src:r,ref:S,onLoad:e=>{g(e.currentTarget,p,_,y,O,b,w)},onError:e=>{j(!0),"empty"!==p&&O(!0),C&&C(e)}})});function x(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...v(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,n),null):(0,i.jsx)(l.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,s.forwardRef)((e,t)=>{let r=(0,s.useContext)(f.RouterContext),n=(0,s.useContext)(c.ImageConfigContext),o=(0,s.useMemo)(()=>{var e;let t=h||n||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),o=t.deviceSizes.sort((e,t)=>e-t),i=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:o,qualities:i}},[n]),{onLoad:a,onLoadingComplete:l}=e,m=(0,s.useRef)(a);(0,s.useEffect)(()=>{m.current=a},[a]);let g=(0,s.useRef)(l);(0,s.useEffect)(()=>{g.current=l},[l]);let[v,_]=(0,s.useState)(!1),[y,O]=(0,s.useState)(!1),{props:j,meta:w}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:o,blurComplete:v,showAltText:y});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{...j,unoptimized:w.unoptimized,placeholder:w.placeholder,fill:w.fill,onLoadRef:m,onLoadingCompleteRef:g,setBlurComplete:_,setShowAltText:O,sizesInput:e.sizes,ref:t}),w.priority?(0,i.jsx)(x,{isAppRouter:!r,imgAttributes:j}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56527:(e,t,r)=>{"use strict";e.exports=r(53339).vendored.contexts.HeadManagerContext},59556:(e,t,r)=>{Promise.resolve().then(r.bind(r,10257))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63110:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:o,blurDataURL:i,objectFit:s}=e,a=n?40*n:t,l=o?40*o:r,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},69922:(e,t,r)=>{"use strict";e.exports=r(53339).vendored.contexts.RouterContext},72878:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},76239:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:n,width:o,quality:i}=e,s=i||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(n)+"&w="+o+"&q="+s+(n.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},77190:()=>{},79551:e=>{"use strict";e.exports=require("url")},80433:(e,t,r)=>{"use strict";e.exports=r(53339).vendored.contexts.ImageConfigContext},88429:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(64996),o=()=>{},i=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function a(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),a(),o(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),o(()=>(r&&(r._pendingUpdate=a),()=>{r&&(r._pendingUpdate=a)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},96304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(64996);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=i(e,n)),t&&(o.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96356:(e,t,r)=>{Promise.resolve().then(r.bind(r,35011))},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var n=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function i({children:e}){return(0,n.jsxs)("html",{lang:"fr",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,n.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,n.jsxs)("body",{className:"font-body antialiased",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,425,808],()=>r(3728));module.exports=n})();