
import type {NextConfig} from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true, // Required for static export
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      }
    ],
  },
  devIndicators: false, // Disabling all dev indicators for production
  output: 'export', // Enable static export for Capacitor
  trailingSlash: true, // For better static hosting
  distDir: 'out', // Capacitor looks in 'out' by default
};

export default nextConfig;

export function exportPathMap(defaultPathMap: any) {
  const paths = { ...defaultPathMap };

  // Exclude admin routes
  const adminRoutes = Object.keys(paths).filter(route => route.includes('/admin'));
  adminRoutes.forEach(route => delete paths[route]);

  // Exclude dynamic game routes (except static ones)
  const gameRoutes = Object.keys(paths).filter(route =>
    /\/jeux\/[^/]+$/.test(route) && !['/jeux', '/jeux/generateur', '/jeux/validation'].includes(route)
  );
  gameRoutes.forEach(route => delete paths[route]);

  // Exclude dynamic stage routes (except static ones)
  const stageRoutes = Object.keys(paths).filter(route =>
    /\/stages\/[^/]+$/.test(route) && route !== '/stages'
  );
  stageRoutes.forEach(route => delete paths[route]);

  return paths;
}
