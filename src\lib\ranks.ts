
import { Medal, Shield, Gem, Crown, Rocket, Trophy } from 'lucide-react';
import type { LucideIcon } from 'lucide-react';

export interface Rank {
    name: string;
    minMissions: number; // Changed from minExploits to minMissions
    icon: LucideIcon;
    color: string;
}

export const rankConfig: Rank[] = [
    { name: 'Bronze', minMissions: 0, icon: Medal, color: '#cd7f32' },
    { name: 'Argent', minMissions: 10, icon: Shield, color: '#c0c0c0' },
    { name: 'Or', minMissions: 25, icon: Trophy, color: '#ffd700' },
    { name: 'Champion', minMissions: 50, icon: Crown, color: '#8a2be2' },
    { name: 'Elite', minMissions: 100, icon: Rocket, color: '#00ffff' },
];


export const getRankForMissions = (missionCount: number): Rank => {
    let currentRank: Rank = rankConfig[0];
    for (const rank of rankConfig) {
        if (missionCount >= rank.minMissions) {
            currentRank = rank;
        } else {
            break;
        }
    }
    return currentRank;
};
