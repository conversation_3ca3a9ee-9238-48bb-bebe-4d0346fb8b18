(()=>{var e={};e.id=45,e.ids=[45],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38786:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var s=t(5853),n=t(60554),a=t(30708),i=t.n(a),o=t(8067),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let p={children:["",{children:["jeux",{children:["[gameId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20846)),"C:\\PL\\COPUN-V5\\src\\app\\jeux\\[gameId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["C:\\PL\\COPUN-V5\\src\\app\\jeux\\[gameId]\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/jeux/[gameId]/page",pathname:"/jeux/[gameId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},46645:(e,r,t)=>{Promise.resolve().then(t.bind(t,20846))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},88501:(e,r,t)=>{Promise.resolve().then(t.bind(t,73052))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[683,425,808,97,824,308],()=>t(38786));module.exports=s})();