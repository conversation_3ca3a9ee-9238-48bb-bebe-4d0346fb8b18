
'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import type { LucideIcon } from 'lucide-react';

interface MenuItem {
  href: string;
  label: string;
  icon: LucideIcon;
}

interface MobileBottomNavProps {
  menuItems: MenuItem[];
}

export function MobileBottomNav({ menuItems }: MobileBottomNavProps) {
  const pathname = usePathname();
  const router = useRouter();
  const [startX, setStartX] = useState<number | null>(null);
  const [swiping, setSwiping] = useState(false);

  const currentIndex = menuItems.findIndex((item) =>
    (item.href === '/dashboard' && pathname === item.href) ||
    (item.href !== '/dashboard' && pathname.startsWith(item.href))
  ) || 0;

  const swipeThreshold = 50;

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX);
    setSwiping(false);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (startX === null) return;

    const currentX = e.touches[0].clientX;
    const deltaX = currentX - startX;
    const deltaY = Math.abs(e.touches[0].clientY - 0); // Assuming startY is 0 for simplicity, but track properly if needed

    if (Math.abs(deltaX) > 10 && Math.abs(deltaY) < 50 && !swiping) {
      setSwiping(true);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (startX === null || !swiping) return;

    const endX = e.changedTouches[0].clientX;
    const deltaX = endX - startX;

    if (Math.abs(deltaX) > swipeThreshold) {
      if (deltaX > 0) {
        // Swipe right, go to previous
        const prevIndex = currentIndex === 0 ? menuItems.length - 1 : currentIndex - 1;
        router.push(menuItems[prevIndex].href);
      } else {
        // Swipe left, go to next
        const nextIndex = (currentIndex + 1) % menuItems.length;
        router.push(menuItems[nextIndex].href);
      }
    }

    setStartX(null);
    setSwiping(false);
  };

  return (
    <motion.nav
      className="md:hidden fixed bottom-0 left-0 right-0 h-16 bg-background border-t border-border flex justify-around items-center z-50"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {menuItems.map((item, index) => {
        const isActive = index === currentIndex;
        const IconComponent = item.icon;
        return (
          <Link href={item.href} key={item.href} className="flex flex-col items-center justify-center text-center w-full h-full">
            <motion.div
              className={cn(
                'flex flex-col items-center justify-center gap-1 p-2 rounded-md transition-colors w-full h-full',
                isActive ? 'text-primary' : 'text-muted-foreground'
              )}
              whileTap={{ scale: 0.95 }}
              initial={{ scale: 1 }}
              animate={{ scale: isActive ? 1.05 : 1 }}
              transition={{ type: 'spring', stiffness: 200, damping: 20 }}
            >
              <IconComponent className="w-6 h-6" />
              <span className="text-xs">{item.label}</span>
            </motion.div>
          </Link>
        );
      })}
    </motion.nav>
  );
}

    