(()=>{var e={};e.id=23,e.ids=[23],e.modules={1070:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var n=r(5853),o=r(60554),s=r(30708),i=r.n(s),a=r(8067),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["contenu",{children:["modifier",{children:["[cardId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97212)),"C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\modifier\\[cardId]\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\modifier\\[cardId]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/contenu/modifier/[cardId]/page",pathname:"/admin/contenu/modifier/[cardId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,r)=>{"use strict";var n=r(96575);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},12915:(e,t,r)=>{Promise.resolve().then(r.bind(r,46054))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29492:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var n=r(64996);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:d,iconNode:l,...c},u)=>(0,n.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:s("lucide",a),...c},[...l.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(d)?d:[d]])),d=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},d)=>(0,n.createElement)(a,{ref:d,iconNode:t,className:s(`lucide-${o(e)}`,r),...i}));return r.displayName=`${e}`,r}},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},46054:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(28625),o=r(64996),s=r(11107);!function(){var e=Error("Cannot find module '@/components/admin/card-editor'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(48382);function a(){let e=(0,s.useParams)().cardId,[t,r]=(0,o.useState)(null),[a,d]=(0,o.useState)(!0);return a?(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsx)(i.A,{className:"w-8 h-8 animate-spin"})}):t?(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/admin/card-editor'");throw e.code="MODULE_NOT_FOUND",e}()),{initialData:t,onSave:t=>{console.log("Updating card:",e,t)},isSaving:!1}):(0,n.jsx)("p",{children:"Fiche non trouv\xe9e."})}},48382:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52827:(e,t,r)=>{Promise.resolve().then(r.bind(r,97212))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77190:()=>{},79551:e=>{"use strict";e.exports=require("url")},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},97212:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\admin\\\\contenu\\\\modifier\\\\[cardId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\modifier\\[cardId]\\page.tsx","default")},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>o});var n=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function s({children:e}){return(0,n.jsxs)("html",{lang:"fr",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,n.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,n.jsxs)("body",{className:"font-body antialiased",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,425,808],()=>r(1070));module.exports=n})();