(()=>{var e={};e.id=239,e.ids=[239],e.modules={2327:()=>{},2727:(e,n,o)=>{Promise.resolve().then(o.t.bind(o,88416,23)),Promise.resolve().then(o.t.bind(o,27342,23)),Promise.resolve().then(o.t.bind(o,74078,23)),Promise.resolve().then(o.t.bind(o,64193,23)),Promise.resolve().then(o.t.bind(o,91573,23)),Promise.resolve().then(o.t.bind(o,95405,23)),Promise.resolve().then(o.t.bind(o,97301,23)),Promise.resolve().then(o.t.bind(o,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4113:(e,n,o)=>{Promise.resolve().then(o.bind(o,17816))},7055:()=>{},9340:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>t});let t=(0,o(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\admin\\\\contenu\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,n,o)=>{"use strict";var t=o(96575);o.o(t,"useParams")&&o.d(n,{useParams:function(){return t.useParams}}),o.o(t,"useRouter")&&o.d(n,{useRouter:function(){return t.useRouter}}),o.o(t,"useSearchParams")&&o.d(n,{useSearchParams:function(){return t.useSearchParams}})},13838:(e,n,o)=>{"use strict";o.r(n),o.d(n,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>d});var t=o(5853),r=o(60554),i=o(30708),s=o.n(i),a=o(8067),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);o.d(n,c);let d={children:["",{children:["admin",{children:["contenu",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,9340)),"C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(o.t.bind(o,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(o.t.bind(o,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\PL\\COPUN-V5\\src\\app\\admin\\contenu\\page.tsx"],u={require:o,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/contenu/page",pathname:"/admin/contenu",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13841:(e,n,o)=>{Promise.resolve().then(o.bind(o,9340))},17816:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>x});var t=o(28625),r=o(64996),i=o(39103);let s=(0,o(29492).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var a=o(20625),c=o(86028),d=o(65983),l=o(78545),u=o(48382),m=o(93948),h=o.n(m),O=o(11107);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let f={COMPRENDRE:{label:"Comprendre",color:"bg-cop-comprendre text-background"},OBSERVER:{label:"Observer",color:"bg-cop-observer text-background"},PROTÉGER:{label:"Prot\xe9ger",color:"bg-cop-proteger text-background"}},p={1:"Niveau 1",2:"Niveau 2-3",3:"Niveau 4-5"};function x(){let{toast:e}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),n=(0,O.useRouter)(),[o,m]=(0,r.useState)([]),[x,N]=(0,r.useState)(!0),[v,j]=(0,r.useTransition)(),[b,_]=(0,r.useState)(""),[E,U]=(0,r.useState)([]),[D,w]=(0,r.useState)([]),[g,C]=(0,r.useState)([]),[M,L]=(0,r.useState)(null),T=(0,r.useMemo)(()=>Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).flatMap(e=>e.themes),[]),F=(0,r.useMemo)(()=>o.filter(e=>{let n=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.find(n=>`q${n.id}`===e.id);if(!n)return!1;let o=b.toLowerCase(),t=!b||e.question.toLowerCase().includes(o)||e.answer.toLowerCase().includes(o),r=!(E.length>0)||E.includes(n.dimension),i=!(D.length>0)||D.includes(e.niveau),s=!(g.length>0)||g.some(n=>e.tags_theme.includes(n));return t&&r&&i&&s}).sort((e,n)=>e.question.localeCompare(n.question)),[o,b,E,D,g]),y=e=>{let n=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.find(n=>`q${n.id}`===e.id);return f[n?.dimension]||{label:"Inconnu",color:"bg-gray-500"}},P=e=>{U(n=>n.includes(e)?n.filter(n=>n!==e):[...n,e])},k=e=>{w(n=>n.includes(e)?n.filter(n=>n!==e):[...n,e])},q=e=>{C(n=>n.includes(e)?n.filter(n=>n!==e):[...n,e])},A=b||E.length>0||D.length>0||g.length>0;return(0,t.jsxs)("div",{className:"flex h-full",children:[(0,t.jsxs)("aside",{className:"hidden md:flex flex-col w-64 border-r",children:[(0,t.jsxs)("div",{className:"p-4 pr-0",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold tracking-tight",children:"Th\xe8mes"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Filtrez par th\xe9matique."})]}),(0,t.jsx)("nav",{className:"flex flex-col gap-1 py-4 pr-4 pl-2",children:Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("h4",{className:"px-2 text-xs font-semibold uppercase text-muted-foreground tracking-wider",children:e.label}),(0,t.jsx)("div",{className:"flex flex-col gap-0.5",children:e.themes.map(e=>{let n=g.includes(e.id),o=e.icon;return(0,t.jsxs)("div",{role:"button",tabIndex:0,onClick:()=>q(e.id),onKeyDown:n=>("Enter"===n.key||" "===n.key)&&q(e.id),className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center justify-start gap-2 h-auto py-1.5 px-2 rounded-md transition-colors cursor-pointer","focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",n?"bg-primary/10 text-primary font-semibold":"text-muted-foreground hover:text-foreground"),children:[(0,t.jsx)(o,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"text-sm",children:e.title})]},e.id)})})]},e.label))})]}),(0,t.jsx)("main",{className:"flex-1 pl-6",children:(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{open:!!M,onOpenChange:e=>!e&&L(null),children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-foreground font-headline",children:"Contenu P\xe9dagogique"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Consultez, filtrez et g\xe9rez l'ensemble des fiches de contenu."})]}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>n.push("/admin/contenu/creer"),children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Cr\xe9er une fiche"]})]}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-grow",children:[(0,t.jsx)(s,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Rechercher par question ou objectif...",className:"pl-9",value:b,onChange:e=>_(e.target.value)})]}),(0,t.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-full sm:w-auto",children:[(0,t.jsx)(a.A,{className:"mr-2 h-4 w-4"}),"Piliers ",E.length>0&&`(${E.length})`]})}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{align:"end",children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Filtrer par pilier"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{}),Object.entries(f).map(([e,{label:n}])=>(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:E.includes(e),onCheckedChange:()=>P(e),children:n},e))]})]}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-full sm:w-auto",children:[(0,t.jsx)(a.A,{className:"mr-2 h-4 w-4"}),"Niveaux ",D.length>0&&`(${D.length})`]})}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{align:"end",children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Filtrer par niveau"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{}),Object.entries(p).map(([e,n])=>(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:D.includes(parseInt(e)),onCheckedChange:()=>k(parseInt(e)),children:n},e))]})]}),A&&(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",onClick:()=>{_(""),U([]),w([]),C([])},children:[(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Effacer"]})]})]})}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-4",children:[F.length," sur ",o.length," fiches affich\xe9es."]}),(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-[45%]",children:"Question / Objectif"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-[25%]",children:"Th\xe8mes"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Pilier & Niveau"}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-right",children:"Actions"})]})}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:x?(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{colSpan:4,className:"h-24 text-center",children:"Chargement..."})}):F.length>0?F.map(e=>{let n=y(e);return(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)("div",{className:"font-medium text-foreground",children:e.question}),(0,t.jsx)("div",{className:"text-muted-foreground text-xs mt-1",children:e.answer})]}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags_theme.map(e=>{let n=T.find(n=>n.id===e);return n?(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",className:"font-normal",children:n.title},e):null})})}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsxs)("div",{className:"flex flex-col gap-2 items-start",children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:n.color,children:n.label}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:p[e.niveau]})]})}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-right",children:(0,t.jsxs)("div",{className:"flex gap-2 justify-end",children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"icon",asChild:!0,children:(0,t.jsx)(h(),{href:`/admin/contenu/modifier/${e.id}`,children:(0,t.jsx)(d.A,{className:"w-4 h-4"})})}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"icon",className:"text-destructive hover:text-destructive",onClick:()=>L(e),children:(0,t.jsx)(l.A,{className:"w-4 h-4"})})})]})})]},e.id)}):(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/table'");throw e.code="MODULE_NOT_FOUND",e}()),{colSpan:4,className:"h-24 text-center",children:"Aucun r\xe9sultat pour vos filtres."})})})]})})]})]}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Confirmer la suppression"}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:['\xcates-vous s\xfbr de vouloir supprimer la fiche "',M?.question,'" ? Cette action est irr\xe9versible.']})]}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Annuler"}),(0,t.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>{M&&j(async()=>{console.log("Deleting card:",M.id),m(e=>e.filter(e=>e.id!==M.id)),e({title:"Fiche supprim\xe9e (Simulation)",description:`La fiche "${M.question}" a \xe9t\xe9 retir\xe9e.`}),L(null)})},disabled:v,children:[v&&(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"})," Supprimer"]})]})]})]})})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20625:(e,n,o)=>{"use strict";o.d(n,{A:()=>t});let t=(0,o(29492).A)("ListFilter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31077:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>r});var t=o(41808);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},39103:(e,n,o)=>{"use strict";o.d(n,{A:()=>t});let t=(0,o(29492).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65983:(e,n,o)=>{"use strict";o.d(n,{A:()=>t});let t=(0,o(29492).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},77190:()=>{},78545:(e,n,o)=>{"use strict";o.d(n,{A:()=>t});let t=(0,o(29492).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},79551:e=>{"use strict";e.exports=require("url")},86028:(e,n,o)=>{"use strict";o.d(n,{A:()=>t});let t=(0,o(29492).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},94479:(e,n,o)=>{Promise.resolve().then(o.t.bind(o,49782,23)),Promise.resolve().then(o.t.bind(o,23552,23)),Promise.resolve().then(o.t.bind(o,30708,23)),Promise.resolve().then(o.t.bind(o,17319,23)),Promise.resolve().then(o.t.bind(o,92079,23)),Promise.resolve().then(o.t.bind(o,8487,23)),Promise.resolve().then(o.t.bind(o,55543,23)),Promise.resolve().then(o.t.bind(o,42241,23))},97898:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>i,metadata:()=>r});var t=o(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),o(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let r={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function i({children:e}){return(0,t.jsxs)("html",{lang:"fr",children:[(0,t.jsxs)("head",{children:[(0,t.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,t.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,t.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,t.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,t.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,t.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,t.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,t.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,t.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,t.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,t.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,t.jsxs)("body",{className:"font-body antialiased",children:[(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,t.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var n=require("../../../webpack-runtime.js");n.C(e);var o=e=>n(n.s=e),t=n.X(0,[683,425,808,97],()=>o(13838));module.exports=t})();