
'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { CardEditor } from '@/components/admin/card-editor';
import { jsonQuestions } from '@/data/etages';
import type { ContentCard } from '@/lib/types';
import { Loader2 } from 'lucide-react';

export default function EditCardPage() {
  const params = useParams();
  const cardId = params.cardId as string;
  const [card, setCard] = useState<ContentCard | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (cardId) {
      const cardData = jsonQuestions.questions_copun.find(q => `q${q.id}` === cardId);
      if (cardData) {
        setCard({
            id: `q${cardData.id}`,
            question: cardData.question,
            answer: cardData.objectif || '',
            option_id: cardData.dimension.toLowerCase(),
            priority: 'essential',
            status: 'validated',
            type: 'Question',
            duration: 0,
            tags_theme: cardData.tags_theme || [],
            tags_filtre: cardData.tags_filtre || [],
            niveau: cardData.niveau,
            tags: [],
            tip: cardData.tip
        });
      }
      setLoading(false);
    }
  }, [cardId]);

  const handleSave = (data: any) => {
    console.log("Updating card:", cardId, data);
    // Here you would call an action to update the data in the database
  }

  if (loading) {
    return <div className="flex items-center justify-center h-full"><Loader2 className="w-8 h-8 animate-spin" /></div>;
  }

  if (!card) {
    return <p>Fiche non trouvée.</p>;
  }

  return (
    <CardEditor 
      initialData={card}
      onSave={handleSave}
      isSaving={false}
    />
  );
}

export const dynamic = 'force-dynamic';
    
