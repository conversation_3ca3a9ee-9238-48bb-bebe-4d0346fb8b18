"use strict";exports.id=824,exports.ids=[824],exports.modules={716:(t,e,i)=>{i.d(e,{j:()=>A});var s=i(35547),n=i(13258);function r(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var o=i(95590),a=i(67981),l=i(67273);function u(t,e){return i=>i>0?e:t}let h=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},d=[o.u,a.B,l.V],c=t=>d.find(e=>e.test(t));function p(t){let e=c(t);if((0,n.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===l.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,o=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,l=2*i-s;n=r(l,s,t+1/3),o=r(l,s,t),a=r(l,s,t-1/3)}else n=o=a=i;return{red:Math.round(255*n),green:Math.round(255*o),blue:Math.round(255*a),alpha:s}}(i)),i}let m=(t,e)=>{let i=p(t),n=p(e);if(!i||!n)return u(t,e);let r={...i};return t=>(r.red=h(i.red,n.red,t),r.green=h(i.green,n.green,t),r.blue=h(i.blue,n.blue,t),r.alpha=(0,s.k)(i.alpha,n.alpha,t),a.B.transform(r))};var f=i(1305),v=i(30053),g=i(89995),y=i(32590);let x=new Set(["none","hidden"]);function P(t,e){return i=>(0,s.k)(t,e,i)}function w(t){return"number"==typeof t?P:"string"==typeof t?(0,y.p)(t)?u:v.y.test(t)?m:S:Array.isArray(t)?T:"object"==typeof t?v.y.test(t)?m:b:u}function T(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function b(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=w(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let S=(t,e)=>{let i=g.f.createTransformer(e),s=(0,g.V)(t),r=(0,g.V)(e);return s.indexes.var.length===r.indexes.var.length&&s.indexes.color.length===r.indexes.color.length&&s.indexes.number.length>=r.indexes.number.length?x.has(t)&&!r.values.length||x.has(e)&&!s.values.length?function(t,e){return x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,f.F)(T(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!=(i=t.values[a])?i:0;s[r]=l,n[o]++}return s}(s,r),r.values),i):((0,n.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),u(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,s.k)(t,e,i):w(t)(t,e)}},1134:(t,e,i)=>{i.d(e,{W:()=>s});let s={skipAnimations:!1,useManualTiming:!1}},1305:(t,e,i)=>{i.d(e,{F:()=>n});let s=(t,e)=>i=>e(t(i)),n=(...t)=>t.reduce(s)},3618:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(64996);let n=i(88838).B?s.useLayoutEffect:s.useEffect},3619:(t,e,i)=>{i.d(e,{OQ:()=>h,bt:()=>l});var s=i(61928),n=i(10820),r=i(60439),o=i(98418);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.k.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},7054:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(64996),n=i(73181);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{t&&a(l)},[t]);let u=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},10343:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>u,px:()=>o,uj:()=>n,vh:()=>a,vw:()=>l});let s=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=s("deg"),r=s("%"),o=s("px"),a=s("vh"),l=s("vw"),u={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},10820:(t,e,i)=>{i.d(e,{v:()=>n});var s=i(58464);class n{constructor(){this.subscriptions=[]}add(t){return(0,s.Kq)(this.subscriptions,t),()=>(0,s.Ai)(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},13258:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>r});var s=i(61695);let n=s.l,r=s.l},15779:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>o});var s=i(57919);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&n.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,o,a,l]=n.match(s.S);return{[t]:parseFloat(r),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},21142:(t,e,i)=>{i.d(e,{G:()=>u});var s=i(61695),n=i(13258),r=i(81854),o=i(64266),a=i(716),l=i(1305);function u(t,e,{clamp:i=!0,ease:h,mixer:d}={}){let c=t.length;if((0,n.V)(c===e.length,"Both input and output ranges must be the same length"),1===c)return()=>e[0];if(2===c&&e[0]===e[1])return()=>e[1];let p=t[0]===t[1];t[0]>t[c-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let n=[],r=i||a.j,o=t.length-1;for(let i=0;i<o;i++){let o=r(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.l:e;o=(0,l.F)(t,o)}n.push(o)}return n}(e,h,d),f=m.length,v=i=>{if(p&&i<t[0])return e[0];let s=0;if(f>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=(0,r.q)(t[s],t[s+1],i);return m[s](n)};return i?e=>v((0,o.q)(t[0],t[c-1],e)):v}},24824:(t,e,i)=>{function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>nD});let n=t=>Array.isArray(t);function r(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function o(t){return"string"==typeof t||Array.isArray(t)}function a(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function l(t,e,i,s){if("function"==typeof e){let[n,r]=a(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=a(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function u(t,e,i){let s=t.getProps();return l(s,e,void 0!==i?i:s.custom,t)}let h=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...h];function c(t){let e;return()=>(void 0===e&&(e=t()),e)}let p=c(()=>void 0!==window.ScrollTimeline);class m{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>p()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class f extends m{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function v(t,e){return t?t[e]||t.default||t:void 0}function g(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function y(t){return"function"==typeof t}function x(t,e){t.timeline=e,t.onfinish=null}let P=t=>Array.isArray(t)&&"number"==typeof t[0],w={linearEasing:void 0},T=function(t,e){let i=c(t);return()=>{var t;return null!=(t=w[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var b,S,A=i(81854);let V=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t((0,A.q)(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},M=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,E={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:M([0,.65,.55,1]),circOut:M([.55,0,1,.45]),backIn:M([.31,.01,.66,-.59]),backOut:M([.33,1.53,.69,.99])},k={x:!1,y:!1};function D(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function C(t){return e=>{"touch"===e.pointerType||k.x||k.y||t(e)}}let R=(t,e)=>!!e&&(t===e||R(t,e.parentElement)),j=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,F=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function B(t){return e=>{"Enter"===e.key&&t(e)}}function O(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let I=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=B(()=>{if(L.has(i))return;O(i,"down");let t=B(()=>{O(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>O(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function U(t){return j(t)&&!(k.x||k.y)}let $=t=>1e3*t,N=t=>t/1e3;var W=i(61695);let G=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],q=new Set(G),K=new Set(["width","height","top","left","right","bottom",...G]),z=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),X=t=>n(t)?t[t.length-1]||0:t;var Y=i(3619);let H=t=>!!(t&&t.getVelocity);function Q(t,e){let i=t.getValue("willChange");if(H(i)&&i.add)return i.add(e)}let Z=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),_="data-"+Z("framerAppearId");var J=i(98418),tt=i(1134);let te={current:!1},ti=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function ts(t,e,i,s){if(t===e&&i===s)return W.l;let n=e=>(function(t,e,i,s,n){let r,o,a=0;do(r=ti(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:ti(n(t),e,s)}let tn=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tr=t=>e=>1-t(1-e),to=ts(.33,1.53,.69,.99),ta=tr(to),tl=tn(ta),tu=t=>(t*=2)<1?.5*ta(t):.5*(2-Math.pow(2,-10*(t-1))),th=t=>1-Math.sin(Math.acos(t)),td=tr(th),tc=tn(th),tp=t=>/^0[^.\s]+$/u.test(t);var tm=i(89995),tf=i(57919);let tv=new Set(["brightness","contrast","saturate","opacity"]);function tg(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tf.S)||[];if(!s)return t;let n=i.replace(s,""),r=+!!tv.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let ty=/\b([a-z-]*)\(.*?\)/gu,tx={...tm.f,getAnimatableNone:t=>{let e=t.match(ty);return e?e.map(tg).join(" "):t}};var tP=i(30053),tw=i(34142),tT=i(10343);let tb={borderWidth:tT.px,borderTopWidth:tT.px,borderRightWidth:tT.px,borderBottomWidth:tT.px,borderLeftWidth:tT.px,borderRadius:tT.px,radius:tT.px,borderTopLeftRadius:tT.px,borderTopRightRadius:tT.px,borderBottomRightRadius:tT.px,borderBottomLeftRadius:tT.px,width:tT.px,maxWidth:tT.px,height:tT.px,maxHeight:tT.px,top:tT.px,right:tT.px,bottom:tT.px,left:tT.px,padding:tT.px,paddingTop:tT.px,paddingRight:tT.px,paddingBottom:tT.px,paddingLeft:tT.px,margin:tT.px,marginTop:tT.px,marginRight:tT.px,marginBottom:tT.px,marginLeft:tT.px,backgroundPositionX:tT.px,backgroundPositionY:tT.px},tS={rotate:tT.uj,rotateX:tT.uj,rotateY:tT.uj,rotateZ:tT.uj,scale:tw.hs,scaleX:tw.hs,scaleY:tw.hs,scaleZ:tw.hs,skew:tT.uj,skewX:tT.uj,skewY:tT.uj,distance:tT.px,translateX:tT.px,translateY:tT.px,translateZ:tT.px,x:tT.px,y:tT.px,z:tT.px,perspective:tT.px,transformPerspective:tT.px,opacity:tw.X4,originX:tT.gQ,originY:tT.gQ,originZ:tT.px},tA={...tw.ai,transform:Math.round},tV={...tb,...tS,zIndex:tA,size:tT.px,fillOpacity:tw.X4,strokeOpacity:tw.X4,numOctaves:tA},tM={...tV,color:tP.y,backgroundColor:tP.y,outlineColor:tP.y,fill:tP.y,stroke:tP.y,borderColor:tP.y,borderTopColor:tP.y,borderRightColor:tP.y,borderBottomColor:tP.y,borderLeftColor:tP.y,filter:tx,WebkitFilter:tx},tE=t=>tM[t];function tk(t,e){let i=tE(t);return i!==tx&&(i=tm.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let tD=new Set(["auto","none","0"]),tC=t=>t===tw.ai||t===tT.px,tR=(t,e)=>parseFloat(t.split(", ")[e]),tj=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return tR(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?tR(e[1],t):0}},tF=new Set(["x","y","z"]),tL=G.filter(t=>!tF.has(t)),tB={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:tj(4,13),y:tj(5,14)};tB.translateX=tB.x,tB.translateY=tB.y;let tO=new Set,tI=!1,tU=!1;function t$(){if(tU){let t=Array.from(tO).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tL.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null==(s=t.getValue(e))||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tU=!1,tI=!1,tO.forEach(t=>t.complete()),tO.clear()}function tN(){tO.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tU=!0)})}class tW{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tO.add(this),tI||(tI=!0,J.Gt.read(tN),J.Gt.resolveKeyframes(t$))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n])if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tO.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tO.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}var tG=i(13258);let tq=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);var tK=i(32590);let tz=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tX=t=>e=>e.test(t),tY=[tw.ai,tT.px,tT.KN,tT.uj,tT.vw,tT.vh,{test:t=>"auto"===t,parse:t=>t}],tH=t=>tY.find(tX(t));class tQ extends tW{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),(0,tK.p)(s))){let n=function t(e,i,s=1){(0,tG.V)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=tz.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return tq(t)?parseFloat(t):t}return(0,tK.p)(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!K.has(i)||2!==t.length)return;let[s,n]=t,r=tH(s),o=tH(n);if(r!==o)if(tC(r)&&tC(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tp(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!tD.has(e)&&(0,tm.V)(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=tk(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tB[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=tB[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}var tZ=i(61928);let t_=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tm.f.test(t)||"0"===t)&&!t.startsWith("url(")),tJ=t=>null!==t;function t0(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(tJ),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class t1{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=tZ.k.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tN(),t$()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=tZ.k.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=t_(n,e),a=t_(r,e);return(0,tG.$)(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||y(i))&&s)}(t,i,s,n))if(te.current||!r){a&&a(t0(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}var t5=i(64266),t3=i(716),t2=i(1305),t9=i(60439);function t6(t,e,i){let s=Math.max(e-5,0);return(0,t9.f)(i-t(s),e-s)}let t4={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function t8(t,e){return t*Math.sqrt(1-e*e)}let t7=["duration","bounce"],et=["stiffness","damping","mass"];function ee(t,e){return e.some(e=>void 0!==t[e])}function ei(t=t4.visualDuration,e=t4.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:t4.velocity,stiffness:t4.stiffness,damping:t4.damping,mass:t4.mass,isResolvedFromDuration:!1,...t};if(!ee(t,et)&&ee(t,t7))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*(0,t5.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:t4.mass,stiffness:s,damping:n}}else{let i=function({duration:t=t4.duration,bounce:e=t4.bounce,velocity:i=t4.velocity,mass:s=t4.mass}){let n,r;(0,tG.$)(t<=$(t4.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=(0,t5.q)(t4.minDamping,t4.maxDamping,o),t=(0,t5.q)(t4.minDuration,t4.maxDuration,N(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/t8(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=t8(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=$(t),isNaN(a))return{stiffness:t4.stiffness,damping:t4.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:t4.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-N(s.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*d)),y=a-o,x=N(Math.sqrt(u/d)),P=5>Math.abs(y);if(n||(n=P?t4.restSpeed.granular:t4.restSpeed.default),r||(r=P?t4.restDelta.granular:t4.restDelta.default),v<1){let t=t8(x,v);i=e=>a-Math.exp(-v*x*e)*((f+v*x*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-x*t)*(y+(f+x*y)*t);else{let t=x*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*x*e),s=Math.min(t*e,300);return a-i*((f+v*x*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0;v<1&&(s=0===t?$(f):t6(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(g(w),2e4),e=V(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function es({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let P=t=>-g*Math.exp(-t/s),w=t=>x+P(t),T=t=>{let e=P(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=ei({keyframes:[m.value,v(m.value)],velocity:t6(w,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}let en=ts(.42,0,1,1),er=ts(0,0,.58,1),eo=ts(.42,0,.58,1),ea=t=>Array.isArray(t)&&"number"!=typeof t[0],el={linear:W.l,easeIn:en,easeInOut:eo,easeOut:er,circIn:th,circInOut:tc,circOut:td,backIn:ta,backInOut:tl,backOut:to,anticipate:tu},eu=t=>{if(P(t)){(0,tG.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return ts(e,i,s,n)}return"string"==typeof t?((0,tG.V)(void 0!==el[t],`Invalid easing type '${t}'`),el[t]):t};var eh=i(21142),ed=i(35547);function ec({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=ea(s)?s.map(eu):eu(s),o={done:!1,value:e[0]},a=(n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=(0,A.q)(0,e,s);t.push((0,ed.k)(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),l=(0,eh.G)(a,e,{ease:Array.isArray(r)?r:e.map(()=>r||eo).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}let ep=t=>{let e=({timestamp:e})=>t(e);return{start:()=>J.Gt.update(e,!0),stop:()=>(0,J.WG)(e),now:()=>J.uv.isProcessing?J.uv.timestamp:tZ.k.now()}},em={decay:es,inertia:es,tween:ec,keyframes:ec,spring:ei},ef=t=>t/100;class ev extends t1{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||tW;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=y(s)?s:em[s]||ec;l!==ec&&"number"!=typeof t[0]&&(e=(0,t2.F)(ef,(0,t3.j)(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=g(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=(0,t5.q)(0,1,i)*h}let P=g?{done:!1,value:a[0]}:x.next(y);o&&(P.value=o(P.value));let{done:w}=P;g||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==s&&(P.value=t0(a,this.options,s)),f&&f(P.value),T&&this.finish(),P}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=$(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=ep,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let eg=new Set(["opacity","clipPath","filter","transform"]),ey=c(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ex={anticipate:tu,backInOut:tl,circInOut:tc};class eP extends t1{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new tQ(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&T()&&r in ex&&(r=ex[r]),y((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&T()||!e||"string"==typeof e&&(e in E||T())||P(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ev({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&T()?V(e,i):P(e)?M(e):Array.isArray(e)?e.map(e=>t(e,i)||E.easeOut):E[e]}(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(x(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(t0(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=$(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return W.l;let{animation:i}=e;x(i,t)}else this.pendingTimeline=t;return W.l}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ev({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=$(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ey()&&i&&eg.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let ew={type:"spring",stiffness:500,damping:25,restSpeed:10},eT=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),eb={type:"keyframes",duration:.8},eS={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},eA=(t,{keyframes:e})=>e.length>2?eb:q.has(t)?t.startsWith("scale")?eT(e[1]):ew:eS,eV=(t,e,i,s={},n,r)=>o=>{let a=v(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=$(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...eA(t,h)}),h.duration&&(h.duration=$(h.duration)),h.repeatDelay&&(h.repeatDelay=$(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(te.current||tt.W.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!r&&void 0!==e.get()){let t=t0(h.keyframes,a);if(void 0!==t)return J.Gt.update(()=>{h.onUpdate(t),h.onComplete()}),new f([])}return!r&&eP.supports(h)?new eP(h):new ev(h)};function eM(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);let h=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let s=t.getValue(e,null!=(r=t.latestValues[e])?r:null),n=l[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...v(o||{},e)},u=!1;if(window.MotionHandoffAnimation){let i=t.props[_];if(i){let t=window.MotionHandoffAnimation(i,e,J.Gt);null!==t&&(a.startTime=t,u=!0)}}Q(t,e),s.start(eV(e,s,n,t.shouldReduceMotion&&K.has(e)?{type:!1}:a,t,u));let c=s.animation;c&&h.push(c)}return a&&Promise.all(h).then(()=>{J.Gt.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=u(t,e)||{};for(let e in n={...n,...i}){let i=X(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,Y.OQ)(i))}}(t,a)})}),h}function eE(t,e,i={}){var s;let n=u(t,e,"exit"===i.type?null==(s=t.presenceContext)?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let o=n?()=>Promise.all(eM(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(ek).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(eE(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function ek(t,e){return t.sortNodePosition(e)}let eD=d.length,eC=[...h].reverse(),eR=h.length;function ej(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function eF(){return{animate:ej(!0),whileInView:ej(),whileHover:ej(),whileTap:ej(),whileDrag:ej(),whileFocus:ej(),exit:ej()}}class eL{constructor(t){this.isMounted=!1,this.node=t}update(){}}class eB extends eL{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>eE(t,e,i)));else if("string"==typeof e)s=eE(t,e,i);else{let n="function"==typeof e?u(t,e,i.custom):e;s=Promise.all(eM(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=eF(),a=!0,l=e=>(i,s)=>{var n;let r=u(t,s,"exit"===e?null==(n=t.presenceContext)?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function h(u){let{props:h}=t,c=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<eD;t++){let s=d[t],n=e.props[s];(o(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<eR;e++){var g,y;let d=eC[e],x=i[d],P=void 0!==h[d]?h[d]:c[d],w=o(P),T=d===u?x.isActive:null;!1===T&&(v=e);let b=P===c[d]&&P!==h[d]&&w;if(b&&a&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...f},!x.isActive&&null===T||!P&&!x.prevProp||s(P)||"boolean"==typeof P)continue;let S=(g=x.prevProp,"string"==typeof(y=P)?y!==g:!!Array.isArray(y)&&!r(y,g)),A=S||d===u&&x.isActive&&!b&&w||e>v&&w,V=!1,M=Array.isArray(P)?P:[P],E=M.reduce(l(d),{});!1===T&&(E={});let{prevResolvedValues:k={}}=x,D={...k,...E},C=e=>{A=!0,m.has(e)&&(V=!0,m.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in D){let e=E[t],i=k[t];if(f.hasOwnProperty(t))continue;let s=!1;(n(e)&&n(i)?r(e,i):e===i)?void 0!==e&&m.has(t)?C(t):x.protectedKeys[t]=!0:null!=e?C(t):m.add(t)}x.prevProp=P,x.prevResolvedValues=E,x.isActive&&(f={...f,...E}),a&&t.blockInitialAnimation&&(A=!1);let R=!(b&&S)||V;A&&R&&p.push(...M.map(t=>({animation:t,options:{type:d}})))}if(m.size){let e={};m.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),p.push({animation:e})}let x=!!p.length;return a&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),a=!1,x?e(p):Promise.resolve()}return{animateChanges:h,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null==(n=t.variantChildren)||n.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=h(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=eF(),a=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let eO=0;class eI extends eL{constructor(){super(...arguments),this.id=eO++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function eU(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function e$(t){return{point:{x:t.pageX,y:t.pageY}}}let eN=t=>e=>j(e)&&t(e,e$(e));function eW(t,e,i,s){return eU(t,e,eN(i),s)}let eG=(t,e)=>Math.abs(t-e);class eq{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=eX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(eG(t.x,e.x)**2+eG(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=J.uv;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=eK(e,this.transformPagePoint),J.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=eX("pointercancel"===t.type?this.lastMoveEventInfo:eK(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!j(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=eK(e$(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=J.uv;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,eX(r,this.history)),this.removeListeners=(0,t2.F)(eW(this.contextWindow,"pointermove",this.handlePointerMove),eW(this.contextWindow,"pointerup",this.handlePointerUp),eW(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,J.WG)(this.updatePoint)}}function eK(t,e){return e?{point:e(t.point)}:t}function ez(t,e){return{x:t.x-e.x,y:t.y-e.y}}function eX({point:t},e){return{point:t,delta:ez(t,eY(e)),offset:ez(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=eY(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>$(.1)));)i--;if(!s)return{x:0,y:0};let r=N(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function eY(t){return t[t.length-1]}function eH(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function eQ(t){return t.max-t.min}function eZ(t,e,i,s=.5){t.origin=s,t.originPoint=(0,ed.k)(e.min,e.max,t.origin),t.scale=eQ(i)/eQ(e),t.translate=(0,ed.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function e_(t,e,i,s){eZ(t.x,e.x,i.x,s?s.originX:void 0),eZ(t.y,e.y,i.y,s?s.originY:void 0)}function eJ(t,e,i){t.min=i.min+e.min,t.max=t.min+eQ(e)}function e0(t,e,i){t.min=e.min-i.min,t.max=t.min+eQ(e)}function e1(t,e,i){e0(t.x,e.x,i.x),e0(t.y,e.y,i.y)}function e5(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function e3(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function e2(t,e,i){return{min:e9(t,e),max:e9(t,i)}}function e9(t,e){return"number"==typeof t?t:t[e]||0}let e6=()=>({translate:0,scale:1,origin:0,originPoint:0}),e4=()=>({x:e6(),y:e6()}),e8=()=>({min:0,max:0}),e7=()=>({x:e8(),y:e8()});function it(t){return[t("x"),t("y")]}function ie({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function ii(t){return void 0===t||1===t}function is({scale:t,scaleX:e,scaleY:i}){return!ii(t)||!ii(e)||!ii(i)}function ir(t){return is(t)||io(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function io(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function ia(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function il(t,e=0,i=1,s,n){t.min=ia(t.min,e,i,s,n),t.max=ia(t.max,e,i,s,n)}function iu(t,{x:e,y:i}){il(t.x,e.translate,e.scale,e.originPoint),il(t.y,i.translate,i.scale,i.originPoint)}function ih(t,e){t.min=t.min+e,t.max=t.max+e}function id(t,e,i,s,n=.5){let r=(0,ed.k)(t.min,t.max,n);il(t,e,i,r,s)}function ic(t,e){id(t.x,e.x,e.scaleX,e.scale,e.originX),id(t.y,e.y,e.scaleY,e.scale,e.originY)}function ip(t,e){return ie(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let im=({current:t})=>t?t.ownerDocument.defaultView:null,iv=new WeakMap;class ig{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=e7(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new eq(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(e$(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(k[t])return null;else return k[t]=!0,()=>{k[t]=!1};return k.x||k.y?null:(k.x=k.y=!0,()=>{k.x=k.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),it(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tT.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=eQ(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&J.Gt.postRender(()=>n(t,e)),Q(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>it(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:im(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&J.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!iy(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,ed.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,ed.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,n=this.constraints;e&&eH(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:e5(t.x,i,n),y:e5(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:e2(t,"left","right"),y:e2(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&it(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!eH(e))return!1;let s=e.current;(0,tG.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=ip(t,i),{scroll:n}=e;return n&&(ih(s.x,n.offset.x),ih(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:e3(t.x,r.x),y:e3(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ie(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(it(o=>{if(!iy(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return Q(this.visualElement,t),i.start(eV(t,i,0,e,this.visualElement,!1))}stopAnimation(){it(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){it(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){it(e=>{let{drag:i}=this.getProps();if(!iy(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-(0,ed.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!eH(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};it(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=eQ(t),n=eQ(e);return n>s?i=(0,A.q)(e.min,e.max-s,t.min):s>n&&(i=(0,A.q)(t.min,t.max-n,e.min)),(0,t5.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),it(e=>{if(!iy(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set((0,ed.k)(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;iv.set(this.visualElement,this);let t=eW(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();eH(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),J.Gt.read(e);let n=eU(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(it(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function iy(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class ix extends eL{constructor(t){super(t),this.removeGroupControls=W.l,this.removeListeners=W.l,this.controls=new ig(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||W.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let iP=t=>(e,i)=>{t&&J.Gt.postRender(()=>t(e,i))};class iw extends eL{constructor(){super(...arguments),this.removePointerDownListener=W.l}onPointerDown(t){this.session=new eq(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:im(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:iP(t),onStart:iP(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&J.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=eW(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iT=i(28625),ib=i(64996),iS=i(7054),iA=i(38911);let iV=(0,ib.createContext)({}),iM={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iE(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ik={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tT.px.test(t))return t;else t=parseFloat(t);let i=iE(t,e.target.x),s=iE(t,e.target.y);return`${i}% ${s}%`}},iD={},{schedule:iC,cancel:iR}=(0,i(74303).I)(queueMicrotask,!1);class ij extends ib.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;Object.assign(iD,iL),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iM.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||J.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),iC.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iF(t){let[e,i]=(0,iS.xQ)(),s=(0,ib.useContext)(iA.L);return(0,iT.jsx)(ij,{...t,layoutGroup:s,switchLayoutGroup:(0,ib.useContext)(iV),isPresent:e,safeToRemove:i})}let iL={borderRadius:{...ik,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ik,borderTopRightRadius:ik,borderBottomLeftRadius:ik,borderBottomRightRadius:ik,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tm.f.parse(t);if(s.length>5)return t;let n=tm.f.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=(0,ed.k)(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};var iB=i(58464);let iO=(t,e)=>t.depth-e.depth;class iI{constructor(){this.children=[],this.isDirty=!1}add(t){(0,iB.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,iB.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iO),this.isDirty=!1,this.children.forEach(t)}}var iU=i(10820);function i$(t){let e=H(t)?t.get():t;return z(e)?e.toValue():e}let iN=["TopLeft","TopRight","BottomLeft","BottomRight"],iW=iN.length,iG=t=>"string"==typeof t?parseFloat(t):t,iq=t=>"number"==typeof t||tT.px.test(t);function iK(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iz=iY(0,.5,td),iX=iY(.5,.95,W.l);function iY(t,e,i){return s=>s<t?0:s>e?1:i((0,A.q)(t,e,s))}function iH(t,e){t.min=e.min,t.max=e.max}function iQ(t,e){iH(t.x,e.x),iH(t.y,e.y)}function iZ(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function i_(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function iJ(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tT.KN.test(e)&&(e=parseFloat(e),e=(0,ed.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,ed.k)(r.min,r.max,s);t===r&&(a-=e),t.min=i_(t.min,e,i,a,n),t.max=i_(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let i0=["x","scaleX","originX"],i1=["y","scaleY","originY"];function i5(t,e,i,s){iJ(t.x,e,i0,i?i.x:void 0,s?s.x:void 0),iJ(t.y,e,i1,i?i.y:void 0,s?s.y:void 0)}function i3(t){return 0===t.translate&&1===t.scale}function i2(t){return i3(t.x)&&i3(t.y)}function i9(t,e){return t.min===e.min&&t.max===e.max}function i6(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function i4(t,e){return i6(t.x,e.x)&&i6(t.y,e.y)}function i8(t){return eQ(t.x)/eQ(t.y)}function i7(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class st{constructor(){this.members=[]}add(t){(0,iB.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,iB.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let se={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},si="undefined"!=typeof window&&void 0!==window.MotionDebug,ss=["","X","Y","Z"],sn={visibility:"hidden"},sr=0;function so(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sa({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=sr++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,si&&(se.totalNodes=se.resolvedTargetDeltas=se.recalculatedProjection=0),this.nodes.forEach(sh),this.nodes.forEach(sg),this.nodes.forEach(sy),this.nodes.forEach(sd),si&&window.MotionDebug.record(se)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iI)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new iU.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tZ.k.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&((0,J.WG)(s),t(r-e))};return J.Gt.read(s,!0),()=>(0,J.WG)(s)}(s,250),iM.hasAnimatedSinceResize&&(iM.hasAnimatedSinceResize=!1,this.nodes.forEach(sv))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||sS,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!i4(this.targetLayout,s)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...v(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||sv(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,J.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sx),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[_];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",J.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sp);return}this.isUpdating||this.nodes.forEach(sm),this.isUpdating=!1,this.nodes.forEach(sf),this.nodes.forEach(sl),this.nodes.forEach(su),this.clearAllSnapshots();let t=tZ.k.now();J.uv.delta=(0,t5.q)(0,1e3/60,t-J.uv.timestamp),J.uv.timestamp=t,J.uv.isProcessing=!0,J.PP.update.process(J.uv),J.PP.preRender.process(J.uv),J.PP.render.process(J.uv),J.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,iC.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sc),this.sharedNodes.forEach(sP)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,J.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){J.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=e7(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!i2(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||ir(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),sM((e=s).x),sM(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return e7();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(sk))){let{scroll:t}=this.root;t&&(ih(i.x,t.offset.x),ih(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=e7();if(iQ(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&iQ(i,t),ih(i.x,n.offset.x),ih(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=e7();iQ(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&ic(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),ir(s.latestValues)&&ic(i,s.latestValues)}return ir(this.latestValues)&&ic(i,this.latestValues),i}removeTransform(t){let e=e7();iQ(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ir(i.latestValues))continue;is(i.latestValues)&&i.updateSnapshot();let s=e7();iQ(s,i.measurePageBox()),i5(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return ir(this.latestValues)&&i5(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e7(),this.relativeTargetOrigin=e7(),e1(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iQ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=e7(),this.targetWithTransforms=e7()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,eJ(i.x,s.x,n.x),eJ(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iQ(this.target,this.layout.layoutBox),iu(this.target,this.targetDelta)):iQ(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e7(),this.relativeTargetOrigin=e7(),e1(this.relativeTargetOrigin,this.target,t.target),iQ(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}si&&se.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||is(this.parent.latestValues)||io(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===J.uv.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;iQ(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ic(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iu(t,r)),s&&ir(n.latestValues)&&ic(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=e7());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(iZ(this.prevProjectionDelta.x,this.projectionDelta.x),iZ(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),e_(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&i7(this.projectionDelta.x,this.prevProjectionDelta.x)&&i7(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),si&&se.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e4(),this.projectionDelta=e4(),this.projectionDeltaWithTransform=e4()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=e4();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=e7(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(sb));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(sw(o.x,t.x,s),sw(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;e1(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=s,sT(p.x,m.x,f.x,v),sT(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,i9(u.x,c.x)&&i9(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=e7()),iQ(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=(0,ed.k)(0,void 0!==i.opacity?i.opacity:1,iz(s)),t.opacityExit=(0,ed.k)(void 0!==e.opacity?e.opacity:1,0,iX(s))):r&&(t.opacity=(0,ed.k)(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<iW;n++){let r=`border${iN[n]}Radius`,o=iK(e,r),a=iK(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||iq(o)===iq(a)?(t[r]=Math.max((0,ed.k)(iG(o),iG(a),s),0),(tT.KN.test(a)||tT.KN.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,ed.k)(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,J.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=J.Gt.update(()=>{iM.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=H(0)?0:(0,Y.OQ)(t);return s.start(eV("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&sE(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||e7();let e=eQ(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=eQ(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}iQ(e,i),ic(e,n),e_(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new st),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&so("z",t,s,this.animationValues);for(let e=0;e<ss.length;e++)so(`rotate${ss[e]}`,t,s,this.animationValues),so(`skew${ss[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sn;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=i$(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=i$(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ir(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,iD){if(void 0===o[t])continue;let{correct:e,applyTo:i}=iD[t],n="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=n}else s[t]=n}return this.options.layoutId&&(s.pointerEvents=r===this?i$(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(sp),this.root.sharedNodes.clear()}}}function sl(t){t.updateLayout()}function su(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?it(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=eQ(s);s.min=e[t].min,s.max=s.min+n}):sE(n,i.layoutBox,e)&&it(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=eQ(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=e4();e_(o,e,i.layoutBox);let a=e4();r?e_(a,t.applyTransform(s,!0),i.measuredBox):e_(a,e,i.layoutBox);let l=!i2(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=e7();e1(o,i.layoutBox,n.layoutBox);let a=e7();e1(a,e,r.layoutBox),i4(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sh(t){si&&se.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function sd(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sc(t){t.clearSnapshot()}function sp(t){t.clearMeasurements()}function sm(t){t.isLayoutDirty=!1}function sf(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function sv(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sg(t){t.resolveTargetDelta()}function sy(t){t.calcProjection()}function sx(t){t.resetSkewAndRotation()}function sP(t){t.removeLeadSnapshot()}function sw(t,e,i){t.translate=(0,ed.k)(e.translate,0,i),t.scale=(0,ed.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sT(t,e,i,s){t.min=(0,ed.k)(e.min,i.min,s),t.max=(0,ed.k)(e.max,i.max,s)}function sb(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sS={duration:.45,ease:[.4,0,.1,1]},sA=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sV=sA("applewebkit/")&&!sA("chrome/")?Math.round:W.l;function sM(t){t.min=sV(t.min),t.max=sV(t.max)}function sE(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(i8(e)-i8(i)))}function sk(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let sD=sa({attachResizeListener:(t,e)=>eU(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sC={current:void 0},sR=sa({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sC.current){let t=new sD({});t.mount(window),t.setOptions({layoutScroll:!0}),sC.current=t}return sC.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sj(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&J.Gt.postRender(()=>n(e,e$(e)))}class sF extends eL{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=D(t,i),o=C(t=>{let{target:i}=t,s=e(t);if("function"!=typeof s||!i)return;let r=C(t=>{s(t),i.removeEventListener("pointerleave",r)});i.addEventListener("pointerleave",r,n)});return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,t=>(sj(this.node,t,"Start"),t=>sj(this.node,t,"End"))))}unmount(){}}class sL extends eL{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,t2.F)(eU(this.node.current,"focus",()=>this.onFocus()),eU(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sB(t,e,i){let{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&J.Gt.postRender(()=>n(e,e$(e)))}class sO extends eL{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=D(t,i),o=t=>{let s=t.currentTarget;if(!U(t)||L.has(s))return;L.add(s);let r=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),U(t)&&L.has(s)&&(L.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||R(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{F.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>I(t,n),n)}),r}(t,t=>(sB(this.node,t,"Start"),(t,{success:e})=>sB(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sI=new WeakMap,sU=new WeakMap,s$=t=>{let e=sI.get(t.target);e&&e(t)},sN=t=>{t.forEach(s$)},sW={some:0,all:1};class sG extends eL{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:sW[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;sU.has(i)||sU.set(i,{});let s=sU.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(sN,{root:t,...e})),s[n]}(e);return sI.set(t,i),s.observe(t),()=>{sI.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sq=(0,ib.createContext)({strict:!1});var sK=i(59708);let sz=(0,ib.createContext)({});function sX(t){return s(t.animate)||d.some(e=>o(t[e]))}function sY(t){return!!(sX(t)||t.variants)}function sH(t){return Array.isArray(t)?t.join(" "):t}var sQ=i(88838);let sZ={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},s_={};for(let t in sZ)s_[t]={isEnabled:e=>sZ[t].some(t=>!!e[t])};let sJ=Symbol.for("motionComponentSymbol");var s0=i(73181),s1=i(3618);let s5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function s3(t){if("string"!=typeof t||t.includes("-"));else if(s5.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var s2=i(51731);let s9=t=>(e,i)=>{let n=(0,ib.useContext)(sz),r=(0,ib.useContext)(s0.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,r,o){let a={latestValues:function(t,e,i,n){let r={},o=n(t,{});for(let t in o)r[t]=i$(o[t]);let{initial:a,animate:u}=t,h=sX(t),d=sY(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?u:a;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=l(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(n,r,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,r);return i?o():(0,s2.M)(o)},s6=(t,e)=>e&&"number"==typeof t?e.transform(t):t,s4={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s8=G.length;function s7(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(q.has(t)){o=!0;continue}if((0,tK.j)(t)){n[t]=i;continue}{let e=s6(i,tV[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<s8;r++){let o=G[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=s6(a,tV[o]);if(!l){n=!1;let e=s4[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nt={offset:"stroke-dashoffset",array:"stroke-dasharray"},ne={offset:"strokeDashoffset",array:"strokeDasharray"};function ni(t,e,i){return"string"==typeof t?t:tT.px.transform(e+i*t)}function ns(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(s7(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=ni(e,t.x,t.width),n=ni(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nt:ne;t[r.offset]=tT.px.transform(-s);let o=tT.px.transform(e),a=tT.px.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let nn=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),nr=()=>({...nn(),attrs:{}}),no=t=>"string"==typeof t&&"svg"===t.toLowerCase();function na(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let nl=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nu(t,e,i,s){for(let i in na(t,e,void 0,s),e.attrs)t.setAttribute(nl.has(i)?i:Z(i),e.attrs[i])}function nh(t,{layout:e,layoutId:i}){return q.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!iD[t]||"opacity"===t)}function nd(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(H(n[o])||e.style&&H(e.style[o])||nh(o,t)||(null==(s=null==i?void 0:i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}function nc(t,e,i){let s=nd(t,e,i);for(let i in t)(H(t[i])||H(e[i]))&&(s[-1!==G.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let np=["x","y","width","height","cx","cy","r"],nm={useVisualState:s9({scrapeMotionValuesFromProps:nc,createRenderState:nr,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(q.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<np.length;i++){let s=np[i];t[s]!==e[s]&&(o=!0)}o&&J.Gt.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,s),J.Gt.render(()=>{ns(s,n,no(i.tagName),t.transformTemplate),nu(i,s)})})}})},nf={useVisualState:s9({scrapeMotionValuesFromProps:nd,createRenderState:nn})};function nv(t,e,i){for(let s in e)H(e[s])||nh(s,i)||(t[s]=e[s])}let ng=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ny(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ng.has(t)}let nx=t=>!ny(t);try{!function(t){t&&(nx=e=>e.startsWith("on")?!ny(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let nP={current:null},nw={current:!1},nT=[...tY,tP.y,tm.f],nb=t=>nT.find(tX(t)),nS=new WeakMap,nA=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class nV{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tW,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tZ.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,J.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=sX(e),this.isVariantNode=sY(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&H(e)&&e.set(a[t],!1)}}mount(t){this.current=t,nS.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nw.current||function(){if(nw.current=!0,sQ.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nP.current=t.matches;t.addListener(e),e()}else nP.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nP.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nS.delete(this.current),this.projection&&this.projection.unmount(),(0,J.WG)(this.notifyUpdate),(0,J.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=q.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&J.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in s_){let e=s_[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):e7()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nA.length;e++){let i=nA[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(H(n))t.addValue(s,n);else if(H(r))t.addValue(s,(0,Y.OQ)(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,(0,Y.OQ)(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,Y.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(tq(s)||tp(s))?s=parseFloat(s):!nb(s)&&tm.f.test(e)&&(s=tk(t,e)),this.setBaseTarget(t,H(s)?s.get():s)),H(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=l(this.props,s,null==(e=this.presenceContext)?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||H(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new iU.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nM extends nV{constructor(){super(...arguments),this.KeyframeResolver=tQ}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;H(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class nE extends nM{constructor(){super(...arguments),this.type="html",this.renderInstance=na}readValueFromInstance(t,e){if(q.has(e)){let t=tE(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=((0,tK.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ip(t,e)}build(t,e,i){s7(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return nd(t,e,i)}}class nk extends nM{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=e7}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(q.has(e)){let t=tE(e);return t&&t.default||0}return e=nl.has(e)?e:Z(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nc(t,e,i)}build(t,e,i){ns(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){nu(t,e,i,s)}mount(t){this.isSVGTag=no(t.tagName),super.mount(t)}}let nD=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((b={animation:{Feature:eB},exit:{Feature:eI},inView:{Feature:sG},tap:{Feature:sO},focus:{Feature:sL},hover:{Feature:sF},pan:{Feature:iw},drag:{Feature:ix,ProjectionNode:sR,MeasureLayout:iF},layout:{ProjectionNode:sR,MeasureLayout:iF}},S=(t,e)=>s3(t)?new nk(e):new nE(e,{allowProjection:t!==ib.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:s,Component:n}){var r,a;function l(t,r){var a,l,u;let h,d={...(0,ib.useContext)(sK.Q),...t,layoutId:function({layoutId:t}){let e=(0,ib.useContext)(iA.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:c}=d,p=function(t){let{initial:e,animate:i}=function(t,e){if(sX(t)){let{initial:e,animate:i}=t;return{initial:!1===e||o(e)?e:void 0,animate:o(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,ib.useContext)(sz));return(0,ib.useMemo)(()=>({initial:e,animate:i}),[sH(e),sH(i)])}(t),m=s(t,c);if(!c&&sQ.B){l=0,u=0,(0,ib.useContext)(sq).strict;let t=function(t){let{drag:e,layout:i}=s_;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(d);h=t.MeasureLayout,p.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,ib.useContext)(sz),l=(0,ib.useContext)(sq),u=(0,ib.useContext)(s0.t),h=(0,ib.useContext)(sK.Q).reducedMotion,d=(0,ib.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,ib.useContext)(iV);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&eH(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,ib.useRef)(!1);(0,ib.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[_],v=(0,ib.useRef)(!!f&&!(null==(r=window.MotionHandoffIsComplete)?void 0:r.call(window,f))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,f)));return(0,s1.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),iC.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,ib.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,f)}),v.current=!1))}),c}(n,m,d,e,t.ProjectionNode)}return(0,iT.jsxs)(sz.Provider,{value:p,children:[h&&p.visualElement?(0,iT.jsx)(h,{visualElement:p.visualElement,...d}):null,i(n,t,(a=p.visualElement,(0,ib.useCallback)(t=>{t&&m.onMount&&m.onMount(t),a&&(t?a.mount(t):a.unmount()),r&&("function"==typeof r?r(t):eH(r)&&(r.current=t))},[a])),m,c,p.visualElement)]})}t&&function(t){for(let e in t)s_[e]={...s_[e],...t[e]}}(t),l.displayName=`motion.${"string"==typeof n?n:`create(${null!=(a=null!=(r=n.displayName)?r:n.name)?a:""})`}`;let u=(0,ib.forwardRef)(l);return u[sJ]=n,u}({...s3(t)?nm:nf,preloadedFeatures:b,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(s3(e)?function(t,e,i,s){let n=(0,ib.useMemo)(()=>{let i=nr();return ns(i,e,no(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nv(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nv(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,ib.useMemo)(()=>{let i=nn();return s7(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(nx(n)||!0===i&&ny(n)||!e&&!ny(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==ib.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,ib.useMemo)(()=>H(u)?u.get():u,[u]);return(0,ib.createElement)(e,{...l,children:h})}}(e),createVisualElement:S,Component:t})}))},30053:(t,e,i)=>{i.d(e,{y:()=>o});var s=i(95590),n=i(67273),r=i(67981);let o={test:t=>r.B.test(t)||s.u.test(t)||n.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):n.V.test(t)?n.V.parse(t):s.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):n.V.transform(t)}},32590:(t,e,i)=>{i.d(e,{j:()=>n,p:()=>o});let s=t=>e=>"string"==typeof e&&e.startsWith(t),n=s("--"),r=s("var(--"),o=t=>!!r(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},34142:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>n,hs:()=>o});var s=i(64266);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...n,transform:t=>(0,s.q)(0,1,t)},o={...n,default:1}},35547:(t,e,i)=>{i.d(e,{k:()=>s});let s=(t,e,i)=>t+(e-t)*i},38911:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(64996).createContext)({})},51731:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(64996);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},57919:(t,e,i)=>{i.d(e,{S:()=>s});let s=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},58464:(t,e,i)=>{function s(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>n,Kq:()=>s})},59708:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(64996).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},60439:(t,e,i)=>{i.d(e,{f:()=>s});function s(t,e){return e?1e3/e*t:0}},61695:(t,e,i)=>{i.d(e,{l:()=>s});let s=t=>t},61928:(t,e,i)=>{let s;i.d(e,{k:()=>a});var n=i(1134),r=i(98418);function o(){s=void 0}let a={now:()=>(void 0===s&&a.set(r.uv.isProcessing||n.W.useManualTiming?r.uv.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(o)}}},62328:(t,e,i)=>{i.d(e,{a:()=>s});let s=t=>Math.round(1e5*t)/1e5},64266:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>i>e?e:i<t?t:i},67273:(t,e,i)=>{i.d(e,{V:()=>a});var s=i(34142),n=i(10343),r=i(62328),o=i(15779);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,r.a)(e))+", "+n.KN.transform((0,r.a)(i))+", "+(0,r.a)(s.X4.transform(o))+")"}},67981:(t,e,i)=>{i.d(e,{B:()=>u});var s=i(64266),n=i(34142),r=i(62328),o=i(15779);let a=t=>(0,s.q)(0,255,t),l={...n.ai,transform:t=>Math.round(a(t))},u={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,r.a)(n.X4.transform(s))+")"}},73181:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(64996).createContext)(null)},74303:(t,e,i)=>{i.d(e,{I:()=>r});var s=i(1134);let n=["read","resolveKeyframes","update","preRender","render","postRender"];function r(t,e){let i=!1,r=!0,o={delta:0,timestamp:0,isProcessing:!1},a=()=>i=!0,l=n.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,s=!1,n=!1,r=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){r.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&s?e:i;return n&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{if(o=t,s){n=!0;return}s=!0,[e,i]=[i,e],e.forEach(a),e.clear(),s=!1,n&&(n=!1,l.process(t))}};return l}(a),t),{}),{read:u,resolveKeyframes:h,update:d,preRender:c,render:p,postRender:m}=l,f=()=>{let n=s.W.useManualTiming?o.timestamp:performance.now();i=!1,o.delta=r?1e3/60:Math.max(Math.min(n-o.timestamp,40),1),o.timestamp=n,o.isProcessing=!0,u.process(o),h.process(o),d.process(o),c.process(o),p.process(o),m.process(o),o.isProcessing=!1,i&&e&&(r=!1,t(f))},v=()=>{i=!0,r=!0,o.isProcessing||t(f)};return{schedule:n.reduce((t,e)=>{let s=l[e];return t[e]=(t,e=!1,n=!1)=>(i||v(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<n.length;e++)l[n[e]].cancel(t)},state:o,steps:l}}},81854:(t,e,i)=>{i.d(e,{q:()=>s});let s=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s}},88838:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},89995:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var s=i(30053);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(57919),o=i(62328);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],o=0,h=e.replace(u,t=>(s.y.test(t)?(n.color.push(o),r.push(l),i.push(s.y.parse(t))):t.startsWith("var(")?(n.var.push(o),r.push("var"),i.push(t)):(n.number.push(o),r.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:n,types:r}}function d(t){return h(t).values}function c(t){let{split:e,types:i}=h(t),n=e.length;return t=>{let r="";for(let u=0;u<n;u++)if(r+=e[u],void 0!==t[u]){let e=i[u];e===a?r+=(0,o.a)(t[u]):e===l?r+=s.y.transform(t[u]):r+=t[u]}return r}}let p=t=>"number"==typeof t?0:t,m={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(r.S))?void 0:e.length)||0)+((null==(i=t.match(n))?void 0:i.length)||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},95590:(t,e,i)=>{i.d(e,{u:()=>n});var s=i(67981);let n={test:(0,i(15779).$)("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:s.B.transform}},98418:(t,e,i)=>{i.d(e,{Gt:()=>n,PP:()=>a,WG:()=>r,uv:()=>o});var s=i(61695);let{schedule:n,cancel:r,state:o,steps:a}=(0,i(74303).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.l,!0)}};