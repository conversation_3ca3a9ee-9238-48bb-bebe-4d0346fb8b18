exports.id=308,exports.ids=[308],exports.modules={2327:()=>{},2727:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,88416,23)),Promise.resolve().then(n.t.bind(n,27342,23)),Promise.resolve().then(n.t.bind(n,74078,23)),Promise.resolve().then(n.t.bind(n,64193,23)),Promise.resolve().then(n.t.bind(n,91573,23)),Promise.resolve().then(n.t.bind(n,95405,23)),Promise.resolve().then(n.t.bind(n,97301,23)),Promise.resolve().then(n.t.bind(n,36159,23))},7055:()=>{},11107:(e,t,n)=>{"use strict";var r=n(96575);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},20662:(e,t,n)=>{"use strict";n.d(t,{N:()=>O});var r=n(28625),o=n(64996),s=n(38911),a=n(51731),i=n(73181),c=n(59708);class l extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t}){let n=(0,o.useId)(),s=(0,o.useRef)(null),a=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:i}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:r,top:o,left:c}=a.current;if(t||!s.current||!e||!r)return;s.current.dataset.motionPopId=n;let l=document.createElement("style");return i&&(l.nonce=i),document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            top: ${o}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[t]),(0,r.jsx)(l,{isPresent:t,childRef:s,sizeRef:a,children:o.cloneElement(e,{ref:s})})}let u=({children:e,initial:t,isPresent:n,onExitComplete:s,custom:c,presenceAffectsLayout:l,mode:u})=>{let h=(0,a.M)(m),p=(0,o.useId)(),x=(0,o.useCallback)(e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;s&&s()},[h,s]),f=(0,o.useMemo)(()=>({id:p,initial:t,isPresent:n,custom:c,onExitComplete:x,register:e=>(h.set(e,!1),()=>h.delete(e))}),l?[Math.random(),x]:[n,x]);return(0,o.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[n]),o.useEffect(()=>{n||h.size||!s||s()},[n]),"popLayout"===u&&(e=(0,r.jsx)(d,{isPresent:n,children:e})),(0,r.jsx)(i.t.Provider,{value:f,children:e})};function m(){return new Map}var h=n(7054);let p=e=>e.key||"";function x(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var f=n(3618);let O=({children:e,custom:t,initial:n=!0,onExitComplete:i,presenceAffectsLayout:c=!0,mode:l="sync",propagate:d=!1})=>{let[m,O]=(0,h.xQ)(d),j=(0,o.useMemo)(()=>x(e),[e]),N=d&&!m?[]:j.map(p),v=(0,o.useRef)(!0),b=(0,o.useRef)(j),g=(0,a.M)(()=>new Map),[_,E]=(0,o.useState)(j),[w,U]=(0,o.useState)(j);(0,f.E)(()=>{v.current=!1,b.current=j;for(let e=0;e<w.length;e++){let t=p(w[e]);N.includes(t)?g.delete(t):!0!==g.get(t)&&g.set(t,!1)}},[w,N.length,N.join("-")]);let D=[];if(j!==_){let e=[...j];for(let t=0;t<w.length;t++){let n=w[t],r=p(n);N.includes(r)||(e.splice(t,0,n),D.push(n))}"wait"===l&&D.length&&(e=D),U(x(e)),E(j);return}let{forceRender:C}=(0,o.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:w.map(e=>{let o=p(e),s=(!d||!!m)&&(j===w||N.includes(o));return(0,r.jsx)(u,{isPresent:s,initial:(!v.current||!!n)&&void 0,custom:s?void 0:t,presenceAffectsLayout:c,mode:l,onExitComplete:s?void 0:()=>{if(!g.has(o))return;g.set(o,!0);let e=!0;g.forEach(t=>{t||(e=!1)}),e&&(null==C||C(),U(b.current),d&&(null==O||O()),i&&i())},children:e},o)})})}},20846:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\jeux\\\\[gameId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\jeux\\[gameId]\\page.tsx","default")},24870:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},31077:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},41756:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},43596:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},61825:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},73052:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>M});var r=n(28625),o=n(64996),s=n(11107),a=n(93948),i=n.n(a),c=n(48382),l=n(61825),d=n(29492);let u=(0,d.A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);var m=n(87735),h=n(24870);let p=(0,d.A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),x=(0,d.A)("ThumbsDown",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]);var f=n(41756),O=n(43596);let j=(0,d.A)("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);var N=n(88808),v=n(20662),b=n(24824);!function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}();let g=({card:e,onNext:t,gameMode:n})=>{switch(e.type){case"triage":return(0,r.jsx)(_,{card:e,onNext:t});case"mots":return(0,r.jsx)(E,{card:e,onNext:t});case"dilemme":return(0,r.jsx)(w,{card:e,onNext:t});case"quizz":return(0,r.jsx)(U,{card:e,onNext:t,gameMode:n});default:return null}},_=({card:e,onNext:t})=>{let[n,s]=(0,o.useState)(null),a=null!==n,i=!!a&&n===e.isTrue,c=e=>{a||s(e)};return(0,r.jsxs)("div",{className:"text-center space-y-6 w-full",children:[(0,r.jsx)("p",{className:"text-xl md:text-2xl font-semibold text-center",children:e.statement}),(0,r.jsxs)("div",{className:"flex gap-4 mt-4 justify-center",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>c(!0),disabled:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-lg px-8 py-6",a&&!0===n&&(i?"bg-green-500 hover:bg-green-500":"bg-red-500 hover:bg-red-500"),a&&!0!==n&&e.isTrue&&"bg-green-500 hover:bg-green-500 opacity-50"),children:[(0,r.jsx)(u,{className:"mr-2"})," Vrai"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>c(!1),disabled:a,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-lg px-8 py-6",a&&!1===n&&(i?"bg-green-500 hover:bg-green-500":"bg-red-500 hover:bg-red-500"),a&&!1!==n&&!e.isTrue&&"bg-green-500 hover:bg-green-500 opacity-50"),children:[(0,r.jsx)(m.A,{className:"mr-2"})," Faux"]})]}),a&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>t(i),className:"mt-4",children:["Suivant ",(0,r.jsx)(h.A,{className:"ml-2"})]})]})},E=({card:e,onNext:t})=>{let[n,s]=(0,o.useState)(!1);return(0,r.jsxs)("div",{className:"space-y-4 text-center",children:[(0,r.jsx)("p",{className:"text-xl md:text-2xl",children:e.definition}),(0,r.jsx)(v.N,{children:n&&(0,r.jsx)(b.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},children:(0,r.jsx)("p",{className:"text-3xl font-bold text-primary",children:e.answer})})}),(0,r.jsx)("div",{className:"flex gap-4 mt-6 justify-center",children:n?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>t(!0),className:"bg-green-500 hover:bg-green-600",children:[(0,r.jsx)(p,{className:"mr-2"})," Bonne r\xe9ponse"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>t(!1),variant:"secondary",children:[(0,r.jsx)(x,{className:"mr-2"})," Mauvaise r\xe9ponse"]})]}):(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>s(!0),variant:"outline",children:"R\xe9v\xe9ler la r\xe9ponse"})})]})},w=({card:e,onNext:t})=>{let[n,s]=(0,o.useState)(!1);return(0,r.jsxs)("div",{className:"space-y-4 w-full text-center",children:[(0,r.jsx)("p",{className:"font-semibold text-xl md:text-2xl text-center mb-4",children:"Tu pr\xe9f\xe8res..."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 text-lg border-l-4 border-orange-400 bg-orange-50/50 dark:bg-orange-900/30",children:[(0,r.jsx)("span",{className:"font-bold text-orange-500",children:"A"}),(0,r.jsx)("p",{children:e.optionA})]}),(0,r.jsxs)("div",{className:"flex items-start gap-3 p-3 text-lg border-l-4 border-indigo-400 bg-indigo-50/50 dark:bg-indigo-900/30",children:[(0,r.jsx)("span",{className:"font-bold text-indigo-500",children:"B"}),(0,r.jsx)("p",{children:e.optionB})]})]}),(0,r.jsx)(v.N,{children:n&&(0,r.jsx)(b.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"overflow-hidden",children:(0,r.jsxs)("div",{className:"p-3 bg-muted/80 rounded-md mt-4 text-left",children:[(0,r.jsx)("h4",{className:"font-semibold text-sm mb-1",children:"Le pourquoi du comment :"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:e.explanation})]})})}),(0,r.jsxs)("div",{className:"flex gap-4 mt-6 justify-center",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"link",size:"sm",onClick:()=>s(e=>!e),className:"p-0 h-auto",children:[(0,r.jsx)(f.A,{className:"mr-2 h-4 w-4"}),n?"Cacher l'explication":"Voir l'explication"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>t(),children:["Suivant ",(0,r.jsx)(h.A,{className:"ml-2"})]})]})]})},U=({card:e,onNext:t,gameMode:n})=>{let[s,a]=(0,o.useState)(null),i=null!==s,c=e=>{if(i&&"validation"===n)return void a(e);i||a(e)};return(0,r.jsxs)("div",{className:"space-y-4 w-full",children:[(0,r.jsx)("p",{className:"font-semibold text-xl md:text-2xl mb-4 text-center",children:e.question}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:e.answers.map((t,o)=>{let a=s===o,l=e.correctAnswerIndex===o,d=i&&"validation"!==n;return(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>c(o),disabled:d,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("justify-start h-auto py-3 text-base whitespace-normal text-left",d&&l&&"bg-green-500 hover:bg-green-500",d&&!l&&a&&"bg-red-500 hover:bg-red-500","validation"===n&&a&&"bg-primary text-primary-foreground hover:bg-primary/90"),variant:a?"default":"outline",children:[(0,r.jsx)("span",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-6 mr-2 font-bold"),children:String.fromCharCode(65+o)}),t]},o)})}),i&&(0,r.jsx)("div",{className:"text-center mt-6",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>{null!==s&&t(s===e.correctAnswerIndex,s)},children:["Suivant ",(0,r.jsx)(h.A,{className:"ml-2"})]})})]})},D=({score:e,total:t,onReset:n})=>{let o=t>0?Math.round(e/t*100):0;return(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-card text-center w-full",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-3xl font-bold",children:"Jeu termin\xe9 !"}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Voici les r\xe9sultats du groupe"})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:(0,r.jsxs)(b.P.div,{initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{type:"spring",stiffness:260,damping:20,delay:.2},className:"flex flex-col items-center justify-center gap-4",children:[(0,r.jsx)(O.A,{className:"w-20 h-20 text-yellow-500"}),(0,r.jsxs)("p",{className:"text-5xl font-bold",children:[e," / ",t]}),(0,r.jsxs)("p",{className:"text-xl text-muted-foreground",children:[o,"% de bonnes r\xe9ponses"]})]})}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"justify-center",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:n,variant:"outline",size:"lg",children:[(0,r.jsx)(j,{className:"mr-2 h-4 w-4"}),"Recommencer"]})})]})},C=({results:e,onReset:t})=>{let n=e.filter(e=>e.isCorrect).length,o=e.length,s=o>0?Math.round(n/o*100):0;return(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-3xl font-bold",children:"Quiz de validation termin\xe9"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Votre score : ",n," / ",o," (",s,"%)"]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-muted-foreground",children:"Voici le d\xe9tail de vos r\xe9ponses. Pour les r\xe9ponses incorrectes, une ressource p\xe9dagogique vous est propos\xe9e."}),(0,r.jsx)("div",{className:"space-y-3 max-h-[40vh] overflow-y-auto pr-3",children:e.map((e,t)=>(0,r.jsx)(y,{result:e},t))})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"justify-center border-t pt-4",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:t,variant:"outline",size:"lg",children:[(0,r.jsx)(j,{className:"mr-2 h-4 w-4"}),"Recommencer le quiz"]})})]})},y=({result:e})=>{let t=(0,o.useMemo)(()=>e.card.related_objective_id?Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.find(t=>`q${t.id}`===e.card.related_objective_id):null,[e.card.related_objective_id]);return(0,r.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("border-l-4 p-3 rounded-r-md",e.isCorrect?"border-green-500 bg-green-500/10":"border-red-500 bg-red-500/10"),children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("p",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("font-semibold text-sm flex-1",e.isCorrect?"text-green-200":"text-red-200"),children:e.card.question}),e.isCorrect?(0,r.jsx)(u,{className:"w-5 h-5 text-green-500 shrink-0"}):(0,r.jsx)(m.A,{className:"w-5 h-5 text-red-500 shrink-0"})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground mt-2",children:[(0,r.jsxs)("p",{children:["Votre r\xe9ponse : ",(0,r.jsx)("span",{className:"font-medium text-foreground",children:e.card.answers[e.userAnswerIndex]})]}),!e.isCorrect&&(0,r.jsxs)("p",{children:["Bonne r\xe9ponse : ",(0,r.jsx)("span",{className:"font-medium text-green-400",children:e.card.answers[e.card.correctAnswerIndex]})]})]}),t&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"link",size:"sm",className:"h-auto p-0 mt-2 text-xs",children:[(0,r.jsx)(N.A,{className:"mr-2 h-3 w-3"})," Voir la fiche ressource"]})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Objectif P\xe9dagogique Associ\xe9"}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:t.question})]}),(0,r.jsxs)("div",{className:"px-4 py-2",children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"L'objectif principal de cette notion est de :"}),(0,r.jsx)("p",{className:"font-semibold mt-1",children:t.answer})]}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/drawer'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:"Fermer"})})})]})]})]})},M=function(){let e=(0,s.useParams)(),t=(0,s.useSearchParams)(),{toast:n}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[a,d]=(0,o.useState)(null),[u,m]=(0,o.useState)([]),[h,p]=(0,o.useState)(!0),[x,f]=(0,o.useState)("play"),[O,j]=(0,o.useState)(0),[N,_]=(0,o.useState)(0),[E,w]=(0,o.useState)(!1),[U,y]=(0,o.useState)([]);e.gameId&&("validation"===e.gameId||parseInt(e.gameId,10)),t.get("objectives");let M=t.get("stageId"),L=()=>{j(0),_(0),w(!1),y([])};if(h)return(0,r.jsxs)("div",{className:"flex justify-center items-center h-64",children:[(0,r.jsx)(c.A,{className:"w-8 h-8 animate-spin text-primary"}),(0,r.jsx)("p",{className:"ml-4 text-muted-foreground",children:"Chargement..."})]});if(!a||0===u.length)return(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)(i(),{href:M?`/stages/${M}`:"/jeux",className:"text-sm text-muted-foreground hover:text-foreground flex items-center gap-1 justify-center",children:[(0,r.jsx)(l.A,{className:"w-4 h-4"}),"Retour"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Contenu indisponible"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsx)("p",{children:"Aucune question n'a pu \xeatre charg\xe9e pour ce jeu ou ce quiz de validation."}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Cela peut arriver si aucun objectif p\xe9dagogique n'est li\xe9 au programme du stage."})]})]})]});let T=u[O],F=a.stage_id?`/stages/${a.stage_id}`:"/jeux";return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-6",children:[(0,r.jsxs)(i(),{href:F,className:"text-sm text-muted-foreground hover:text-foreground flex items-center gap-1",children:[(0,r.jsx)(l.A,{className:"w-4 h-4"}),"Retour"]}),(0,r.jsxs)("div",{className:"text-center space-y-1",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold font-headline",children:a.title}),(0,r.jsxs)("p",{className:"text-lg text-muted-foreground",children:["Th\xe8me : ",a.theme]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/progress'");throw e.code="MODULE_NOT_FOUND",e}()),{value:E?100:O/u.length*100}),(0,r.jsxs)("p",{className:"text-center text-sm text-muted-foreground",children:["Question ",E?u.length:O+1," / ",u.length]})]}),(0,r.jsx)("div",{className:"min-h-[450px] flex flex-col justify-center items-center",children:(0,r.jsx)(v.N,{mode:"wait",children:E?(0,r.jsx)(b.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:"w-full",children:"validation"===x?(0,r.jsx)(C,{results:U,onReset:L}):(0,r.jsx)(D,{score:N,total:u.length,onReset:L})},"results"):T?(0,r.jsx)(b.P.div,{initial:{opacity:0,x:300,scale:.8},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:-300,scale:.8},transition:{duration:.4,ease:"easeInOut"},className:"w-full",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"shadow-xl overflow-hidden",children:[(0,r.jsx)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("h-2",{triage:"bg-blue-500",mots:"bg-yellow-500",dilemme:"bg-purple-500",quizz:"bg-teal-500"}[T.type])}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl",children:{triage:"Le Triage C\xf4tier",mots:"Les Mots en Rafale",dilemme:"Le Dilemme du Marin",quizz:"Le Grand Quizz"}[T.type]})}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"min-h-[250px] flex flex-col justify-center items-center p-6",children:(0,r.jsx)(g,{card:T,onNext:(e,t)=>{let r=u[O];if(e&&_(e=>e+1),"validation"===x&&"quizz"===r.type&&void 0!==t&&y(n=>[...n,{card:r,userAnswerIndex:t,isCorrect:!0===e}]),O<u.length-1)j(e=>e+1);else{w(!0);let t=e?N+1:N,r=u.length>0?Math.round(t/u.length*100):0;if("play"===x&&r>=80){let e="badge_animateur_pedagogique";"true"!==localStorage.getItem(e)&&(localStorage.setItem(e,"true"),n({title:"\uD83C\uDFC6 Troph\xe9e D\xe9bloqu\xe9 !",description:"Animateur P\xe9dagogique : Votre groupe a obtenu plus de 80% de bonnes r\xe9ponses !",duration:5e3}))}if("validation"===x){let e=localStorage.getItem("econav_username");if(e&&a){let t={user_id:e,theme:a.theme,score:r,total_questions:u.length};Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(t)}}}},gameMode:x})})]})},O):(0,r.jsx)("p",{children:"Aucune carte valide pour ce jeu ou quiz."})})})]})}},77190:()=>{},87735:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},88808:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},94479:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,49782,23)),Promise.resolve().then(n.t.bind(n,23552,23)),Promise.resolve().then(n.t.bind(n,30708,23)),Promise.resolve().then(n.t.bind(n,17319,23)),Promise.resolve().then(n.t.bind(n,92079,23)),Promise.resolve().then(n.t.bind(n,8487,23)),Promise.resolve().then(n.t.bind(n,55543,23)),Promise.resolve().then(n.t.bind(n,42241,23))},97898:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s,metadata:()=>o});var r=n(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),n(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function s({children:e}){return(0,r.jsxs)("html",{lang:"fr",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,r.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,r.jsxs)("body",{className:"font-body antialiased",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};