

'use client';

import React, { useState, useEffect, useCallback, useTransition, useMemo, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent, UniqueIdentifier } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy, useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import { ChevronLeft, PlusCircle, Calendar, Users, Gamepad2, Settings, ListChecks, Pencil, Save, Loader2, AlertTriangle, ChevronsDown, ChevronsUp, Bookmark, Lightbulb, CheckCircle, Trash2, ArrowRight, ChevronDown, ChevronsRight, ListFilter, ChevronRight as ChevronRightIcon, Shield, Plus, Lock, Unlock, Binoculars, Camera, ShieldQuestion, Map, MessageSquare, Recycle, Search, Milestone, Anchor, Wind, Trophy, Check, Video, Ban, CircleDot, FileCheck, FileText, BookOpen, Compass, Telescope, GraduationCap, Library, Waves, User, Wand, Download, GripVertical, Fish, Undo2 } from 'lucide-react';
import { Button, buttonVariants } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { format, parseISO } from 'date-fns';
import { fr } from 'date-fns/locale';
import type { Stage, Sortie, Game, EtagesData, ContentCard, Option, GrandTheme, StageType } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';
import { getStageById, getSortiesForStage, saveOrUpdateProgramForStage, getGamesForStage, getEtagesData } from '@/app/actions';
import { cn } from '@/lib/utils';
import { jsonQuestions, groupedThemes } from '@/data/etages';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { DropdownMenu, DropdownMenuCheckboxItem, DropdownMenuContent, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogHeader, AlertDialogTitle, AlertDialogFooter } from "@/components/ui/alert-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { MissionsTab } from '@/components/missions-tab';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";




const allGrandThemes = groupedThemes.flatMap(g => g.themes);
const AXE_CONFIG: { [key: string]: { icon: React.ElementType, label: string } } = {
    comprendre: { icon: BookOpen, label: 'Comprendre' },
    observer: { icon: Users, label: 'Observer' },
    proteger: { icon: Settings, label: 'Protéger' },
};

const PILLAR_STYLES: { [key: string]: { badge: string; filterBadge: string, border: string, bg: string, text: string, icon: React.ElementType } } = {
    comprendre: { badge: 'bg-cop-comprendre text-background hover:bg-cop-comprendre', filterBadge: 'border-cop-comprendre text-cop-comprendre', border: 'border-cop-comprendre', bg: 'bg-cop-comprendre', text: 'text-cop-comprendre', icon: BookOpen },
    observer:   { badge: 'bg-cop-observer text-background hover:bg-cop-observer', filterBadge: 'border-cop-observer text-cop-observer', border: 'border-cop-observer', bg: 'bg-cop-observer', text: 'text-cop-observer', icon: Telescope },
    proteger:   { badge: 'bg-cop-proteger text-background hover:bg-cop-proteger', filterBadge: 'border-cop-proteger text-cop-proteger', border: 'border-cop-proteger', bg: 'bg-cop-proteger', text: 'text-cop-proteger', icon: Shield },
};

export default function StageDetailPage() {
    const router = useRouter();
    const params = useParams();
    const stageId = params.stageId ? parseInt(params.stageId as string, 10) : null;
    const { toast } = useToast();

    // --- State for Stage Data ---
    const [stage, setStage] = useState<Stage | null>(null);
    const [sorties, setSorties] = useState<Sortie[]>([]);
    const [games, setGames] = useState<Game[]>([]);
    const [etagesData, setEtagesData] = useState<EtagesData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // --- State for UI ---
    const [activeView, setActiveView] = useState<'objectives' | 'programme' | 'missions' | 'games'>('objectives');
    
    // --- State for Objectives Tab ---
    const [allObjectives, setAllObjectives] = useState<ContentCard[]>([]);
    const [completedObjectives, setCompletedObjectives] = useState<Set<string>>(new Set());

    const programThemes = useMemo(() => {
        const programSortie = sorties.find(s => s.selected_content?.program?.length) || sorties[0];
        if (programSortie && programSortie.selected_content?.themes) {
            const themeTitles = programSortie.selected_content.themes as string[];
            return allGrandThemes
                .filter(theme => themeTitles.includes(theme.title))
                .map(theme => theme.id);
        }
        return [];
    }, [sorties]);
    
    // --- Data fetching and processing ---
    const fetchData = useCallback(async () => {
        if (!stageId) {
            setError("ID de stage manquant.");
            setLoading(false);
            return;
        }
        setLoading(true);
        setError(null);
        try {
            const [stageData, sortiesData, gamesData, etages] = await Promise.all([
                getStageById(stageId),
                getSortiesForStage(stageId),
                getGamesForStage(stageId),
                getEtagesData(),
            ]);
            
            if (!stageData || !etages) {
                setError("Impossible de charger les données critiques.");
                setLoading(false);
                return;
            }

            setStage(stageData);
            setSorties(sortiesData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()));
            setGames(gamesData);
            setEtagesData(etages);

            const allC: ContentCard[] = jsonQuestions.questions_copun.map(q => ({
              id: `q${q.id}`, question: q.question, answer: q.objectif || '', option_id: q.dimension.toLowerCase(),
              priority: 'essential', status: 'validated', type: 'Question', duration: 0, 
              tags_theme: q.tags_theme || [], tags_filtre: q.tags_filtre || [],
              niveau: q.niveau, tags: [], tip: q.tip
            }));

            const programSortie = sortiesData.find(s => s.selected_content?.program?.length) || sortiesData[0];

            if (programSortie && programSortie.selected_content?.program) {
                const currentProgramCardIds = new Set<string>(programSortie.selected_content.program);
                const objectives = allC.filter(card => currentProgramCardIds.has(card.id));
                setAllObjectives(objectives);
            } else {
                 setAllObjectives([]);
            }
            
            const completedFromStorage = localStorage.getItem(`completed_objectives_${stageId}`);
            if (completedFromStorage) setCompletedObjectives(new Set(JSON.parse(completedFromStorage)));


        } catch (err) {
            console.error(err);
            setError("Une erreur est survenue lors du chargement des données.");
        } finally {
            setLoading(false);
        }
    }, [stageId]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const onToggleObjective = (cardId: string) => {
        const newSet = new Set(completedObjectives);
        newSet.has(cardId) ? newSet.delete(cardId) : newSet.add(cardId);
        setCompletedObjectives(newSet);
        if (stageId) localStorage.setItem(`completed_objectives_${stageId}`, JSON.stringify(Array.from(newSet)));
    };
    

    // --- Render Logic ---
    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-[calc(100vh-200px)]">
                <Loader2 className="w-8 h-8 animate-spin text-primary" />
                <p className="ml-4 text-muted-foreground">Chargement du stage...</p>
            </div>
        );
    }
    
    if (error || !stage || !etagesData) {
        return (
            <Card className="m-auto mt-10 max-w-lg text-center border-destructive">
                <CardHeader><CardTitle className="flex items-center justify-center gap-2 text-destructive"><AlertTriangle/> Erreur</CardTitle></CardHeader>
                <CardContent><p className="text-muted-foreground">{error || "Données du stage introuvables."}</p></CardContent>
                <CardFooter><Button variant="outline" asChild className="w-full"><Link href="/stages"><ChevronLeft className="mr-2 h-4 w-4"/> Retour</Link></Button></CardFooter>
            </Card>
        );
    }

    const [mainTitle, ...subtitles] = stage.title.split(' - ');
    const subtitle = subtitles.join(' - ');

    return (
        <div className="space-y-6">
            <Link href="/stages" className="text-sm text-muted-foreground hover:text-foreground flex items-center gap-1">
                <ChevronLeft className="w-4 h-4" />
                Retour à tous les stages
            </Link>
            
            <Card className="overflow-hidden relative bg-stage-header bg-cover bg-center">
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/0" />
                 <CardContent className="relative text-white p-6 space-y-6">
                    <CardHeader className="p-0">
                        <CardTitle className="text-3xl font-bold font-headline">{mainTitle}</CardTitle>
                        {subtitle && <CardDescription className="text-lg text-white/90">{subtitle}</CardDescription>}
                        <div className="flex flex-col sm:flex-row sm:items-center gap-x-4 gap-y-1 pt-2 text-sm text-white/80">
                            <span className="flex items-center gap-1.5"><Calendar className="w-4 h-4" /> {format(parseISO(stage.start_date), "d MMM", { locale: fr })} - {format(parseISO(stage.end_date), "d MMM yyyy", { locale: fr })}</span>
                            <span className="flex items-center gap-1.5"><Users className="w-4 h-4" /> {stage.participants} participants</span>
                        </div>
                    </CardHeader>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        <Button variant={activeView === 'objectives' ? 'secondary' : 'outline'} className={cn(activeView !== 'objectives' && "bg-white/10 border-white/20 text-white hover:bg-white/20")} onClick={() => setActiveView('objectives')}><ListChecks className="mr-2 h-4 w-4"/>Objectifs</Button>
                        <Button variant={activeView === 'programme' ? 'secondary' : 'outline'} className={cn(activeView !== 'programme' && "bg-white/10 border-white/20 text-white hover:bg-white/20")} onClick={() => setActiveView('programme')}><FileText className="mr-2 h-4 w-4"/>Programme</Button>
                        <Button variant={activeView === 'missions' ? 'secondary' : 'outline'} className={cn(activeView !== 'missions' && "bg-white/10 border-white/20 text-white hover:bg-white/20")} onClick={() => setActiveView('missions')}><Trophy className="mr-2 h-4 w-4"/>Missions</Button>
                        <Button variant={activeView === 'games' ? 'secondary' : 'outline'} className={cn(activeView !== 'games' && "bg-white/10 border-white/20 text-white hover:bg-white/20")} onClick={() => setActiveView('games')}><Gamepad2 className="mr-2 h-4 w-4"/>Jeux</Button>
                    </div>
                </CardContent>
            </Card>
            
            <div className="mt-6">
                {activeView === 'objectives' && (
                    <ObjectivesView 
                        objectives={allObjectives}
                        completedObjectives={completedObjectives}
                        onToggleObjective={onToggleObjective}
                    />
                )}
                 {activeView === 'programme' && (
                   <ProgrammeBuilder
                        stage={stage}
                        sorties={sorties}
                        etagesData={etagesData}
                        onSave={(newlySelectedCards) => {
                            setAllObjectives(newlySelectedCards);
                            fetchData(); 
                        }}
                    />
                 )}
                {activeView === 'missions' && stageId && (
                    <MissionsTab 
                       stageId={stageId}
                       stageType={stage.type as StageType}
                       stageThemes={programThemes}
                    />
                )}
                {activeView === 'games' && (
                    <GamesView games={games} stageId={stage.id} />
                )}
            </div>
        </div>
    )
}

// --- Sub-components for Tabs ---

const ObjectivesView = ({ objectives, completedObjectives, onToggleObjective }: { objectives: ContentCard[], completedObjectives: Set<string>, onToggleObjective: (cardId: string) => void }) => {
    const [activeThemeFilters, setActiveThemeFilters] = useState<string[]>([]);
    const [showOnlyNotSeen, setShowOnlyNotSeen] = useState(false);
    
    const themesInProgram = useMemo(() => {
        const themeIds = new Set<string>();
        objectives.forEach(card => {
            card.tags_theme.forEach(themeId => themeIds.add(themeId));
        });
        return allGrandThemes.filter(theme => themeIds.has(theme.id));
    }, [objectives]);
    
    const objectivesByPillar = useMemo(() => {
        const grouped: { [pillar: string]: ContentCard[] } = { comprendre: [], observer: [], proteger: [] };
        
        let filteredObjectives = objectives;

        if (showOnlyNotSeen) {
            filteredObjectives = filteredObjectives.filter(card => !completedObjectives.has(card.id));
        }

        if (activeThemeFilters.length > 0) {
            filteredObjectives = filteredObjectives.filter(card => 
                activeThemeFilters.some(themeId => card.tags_theme.includes(themeId))
            );
        }

        filteredObjectives.forEach((card: ContentCard) => {
            const pillar = card.option_id as keyof typeof grouped;
            if (grouped[pillar] && !grouped[pillar].some(c => c.id === card.id)) {
                grouped[pillar].push(card);
            }
        });
        return grouped;
    }, [objectives, activeThemeFilters, showOnlyNotSeen, completedObjectives]);
    
    const handleThemeFilterToggle = (themeId: string) => {
        setActiveThemeFilters(prev => 
            prev.includes(themeId) 
                ? prev.filter(id => id !== themeId)
                : [...prev, themeId]
        );
    };

    if (objectives.length === 0) {
        return (
            <Card>
                <CardContent className="text-center py-16 px-4">
                    <h3 className="text-lg font-semibold">Le programme est vide</h3>
                    <p className="text-muted-foreground mt-1 mb-4">Allez dans l'onglet "Programme" pour définir les objectifs de ce stage.</p>
                </CardContent>
            </Card>
        );
    }
    
    return (
        <div className="space-y-8">
            <Card>
                <CardContent className="p-4 flex flex-col sm:flex-row gap-4">
                     <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="w-full sm:w-auto">
                                <ListFilter className="mr-2 h-4 w-4" />
                                Thèmes {activeThemeFilters.length > 0 && `(${activeThemeFilters.length})`}
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuLabel>Filtrer par thème</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {themesInProgram.map(theme => (
                                <DropdownMenuCheckboxItem
                                    key={theme.id}
                                    checked={activeThemeFilters.includes(theme.id)}
                                    onCheckedChange={() => handleThemeFilterToggle(theme.id)}
                                >
                                    {theme.title}
                                </DropdownMenuCheckboxItem>
                            ))}
                        </DropdownMenuContent>
                     </DropdownMenu>

                     <div className="flex items-center space-x-2">
                        <Checkbox id="show-not-seen" checked={showOnlyNotSeen} onCheckedChange={(checked) => setShowOnlyNotSeen(!!checked)} />
                        <label htmlFor="show-not-seen" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                            Afficher uniquement les non vus
                        </label>
                    </div>
                </CardContent>
            </Card>

            {Object.entries(objectivesByPillar).map(([pillar, cards]) => {
                const { icon: PillarIcon, label } = AXE_CONFIG[pillar as keyof typeof AXE_CONFIG] || { icon: 'div', label: pillar };
                const pillarStyle = PILLAR_STYLES[pillar as keyof typeof PILLAR_STYLES] || {};
                
                if (cards.length === 0) return null;

                return (
                    <div key={pillar}>
                        <h3 className={cn("text-xl font-semibold flex items-center gap-3 mb-4", pillarStyle.text)}>
                            <PillarIcon className="w-6 h-6" />{label}
                        </h3>
                        <Accordion type="multiple" className="w-full space-y-2">
                            {cards.map((card: ContentCard) => (
                                <AccordionItem value={card.id} key={card.id} className="border-b-0">
                                    <Card className={cn("transition-all overflow-hidden", completedObjectives.has(card.id) ? 'bg-card opacity-50' : 'bg-card')}>
                                        <div className="flex items-center p-1 justify-between">
                                            <div className="flex items-center flex-grow">
                                                <div className={cn("w-1.5 h-auto self-stretch rounded-full shrink-0", pillarStyle.bg)}></div>
                                                <AccordionTrigger className="flex-grow p-3 text-left hover:no-underline">
                                                    <div className="flex flex-col items-start gap-2 text-left">
                                                        <div className="flex flex-wrap gap-1">
                                                          {card.tags_theme.map(themeId => {
                                                            const theme = allGrandThemes.find(t => t.id === themeId);
                                                            return theme ? <Badge key={themeId} variant="outline" className="font-normal">{theme.title}</Badge> : null;
                                                          })}
                                                        </div>
                                                        <p className="font-medium text-foreground text-sm text-left">{card.question}</p>
                                                    </div>
                                                </AccordionTrigger>
                                            </div>
                                            
                                            <div className="flex items-center gap-2 pl-2 pr-1 shrink-0">
                                                <ValidationSlider
                                                    isCompleted={completedObjectives.has(card.id)}
                                                    onToggle={() => onToggleObjective(card.id)}
                                                />
                                                <AccordionTrigger className="p-1 hover:no-underline [&>svg]:mx-auto">
                                                    <ChevronDown className="w-5 h-5 text-muted-foreground transition-transform duration-200" />
                                                </AccordionTrigger>
                                            </div>
                                        </div>
                                        <AccordionContent>
                                            <div className="border-t mx-3"></div>
                                            <div className="px-3 pb-3 pt-2 text-muted-foreground text-sm space-y-2">
                                                <p><span className="font-semibold text-foreground/80">Objectif:</span> {card.answer}</p>
                                                {card.tip && <p><span className="font-semibold text-foreground/80">Conseil:</span> {card.tip}</p>}
                                            </div>
                                        </AccordionContent>
                                    </Card>
                                </AccordionItem>
                            ))}
                        </Accordion>
                    </div>
                );
            })}
        </div>
    );
};

type CardContainer = Record<"available" | "selected", ContentCard[]>;

const ProgrammeBuilder = ({ 
    stage, 
    sorties,
    etagesData, 
    onSave,
}: { 
    stage: Stage, 
    sorties: Sortie[],
    etagesData: EtagesData, 
    onSave: (newlySelectedCards: ContentCard[]) => void,
}) => {
    const { toast } = useToast();
    const [isSaving, startSaveTransition] = useTransition();

    const getInitialState = useCallback(() => {
        const allCards: ContentCard[] = jsonQuestions.questions_copun.map(q => ({
            id: `q${q.id}`, question: q.question, answer: q.objectif || '', option_id: q.dimension.toLowerCase(),
            priority: 'essential', status: 'validated', type: 'Question', duration: 0, 
            tags_theme: q.tags_theme || [], tags_filtre: q.tags_filtre || [],
            niveau: q.niveau, tags: [], tip: q.tip
        }));

        const programSortie = sorties.find(s => s.selected_content?.program?.length) || sorties[0];
        
        let initialSelectedCardIds = new Set<string>();
        let initialLevel = 0;
        let initialThemeIds: string[] = [];

        if (programSortie) {
            const content = programSortie.selected_content || {};
            const notions = programSortie.selected_notions || {};
            if (content.program) initialSelectedCardIds = new Set(content.program);
            if (notions.niveau) initialLevel = notions.niveau;
            if (content.themes) {
                initialThemeIds = (content.themes as string[]).map(title => allGrandThemes.find(t => t.title === title)?.id).filter((id): id is string => !!id);
            }
        }

        const selected = allCards.filter(c => initialSelectedCardIds.has(c.id));
        const available = allCards.filter(c => !initialSelectedCardIds.has(c.id));

        return {
            items: { available, selected },
            level: initialLevel,
            themeIds: initialThemeIds,
        };
    }, [sorties]);

    const [level, setLevel] = useState(getInitialState().level);
    const [themeIds, setThemeIds] = useState(getInitialState().themeIds);
    const [items, setItems] = useState<CardContainer>(getInitialState().items);
    const [activeTab, setActiveTab] = useState('explore');
    const [pillarFilter, setPillarFilter] = useState<string | null>(null);
    
    useEffect(() => {
      const state = getInitialState();
      setLevel(state.level);
      setThemeIds(state.themeIds);
      setItems(state.items);
    }, [sorties, getInitialState]);


    const sensors = useSensors(
      useSensor(PointerSensor),
      useSensor(KeyboardSensor, {
        coordinateGetter: sortableKeyboardCoordinates,
      })
    );
    
    const handleDragEnd = (event: DragEndEvent) => {
        const { active, over } = event;
        if (!over || active.id === over.id) return;

        setItems(prev => {
            const oldIndex = prev.selected.findIndex(item => item.id === active.id);
            const newIndex = prev.selected.findIndex(item => item.id === over.id);
            return {
                ...prev,
                selected: arrayMove(prev.selected, oldIndex, newIndex)
            };
        });
    };

    const addCard = (card: ContentCard) => {
        setItems(prev => ({
            available: prev.available.filter(c => c.id !== card.id),
            selected: [...prev.selected, card]
        }));
    };

    const removeCard = (card: ContentCard) => {
        setItems(prev => ({
            selected: prev.selected.filter(c => c.id !== card.id),
            available: [...prev.available, card].sort((a,b) => a.question.localeCompare(b.question))
        }));
    };

    const handleSaveProgram = () => {
        startSaveTransition(async () => {
            if (!stage) return;
            const mainThemeTitles = themeIds.map(id => allGrandThemes.find(t => t.id === id)?.title).filter((t): t is string => !!t);

            const result = await saveOrUpdateProgramForStage(stage.id, level, mainThemeTitles, items.selected.map(c => c.id));

            if (result.success) {
                toast({ title: "Programme sauvegardé", description: "Le programme a été mis à jour avec succès." });
                onSave(items.selected);
            } else {
                toast({ title: "Erreur", description: result.error || "La sauvegarde a échoué.", variant: 'destructive' });
            }
        });
    };

    const filteredAvailableCards = useMemo(() => {
        let cards = items.available
            .filter(card => card.niveau === level + 1);

        if (themeIds.length > 0) {
            cards = cards.filter(card => themeIds.some(themeId => card.tags_theme.includes(themeId)));
        }
        
        return cards;
    }, [items.available, level, themeIds]);

    return (
      <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Étape 1 : À qui je parle ? (Niveau du groupe)</CardTitle>
                </CardHeader>
                <CardContent>
                    {/* Mobile-first Select */}
                    <div className="sm:hidden">
                        <Select value={level.toString()} onValueChange={(v) => v && setLevel(parseInt(v))}>
                            <SelectTrigger>
                                <SelectValue placeholder="Sélectionnez un niveau" />
                            </SelectTrigger>
                            <SelectContent>
                                {etagesData.niveau.options.map((option, index) => (
                                    <SelectItem key={option.id} value={index.toString()}>
                                        {option.label}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    {/* Desktop Toggle Group */}
                    <ToggleGroup type="single" value={level.toString()} onValueChange={(v) => v && setLevel(parseInt(v))} className="hidden sm:grid sm:grid-cols-3 gap-2">
                        {etagesData.niveau.options.map((option, index) => (
                            <ToggleGroupItem key={option.id} value={index.toString()} className="h-auto flex-col items-start p-3 data-[state=on]:bg-primary/10 data-[state=on]:text-primary data-[state=on]:border-primary">
                                <p className="font-semibold">{option.label}</p>
                                <p className="text-xs text-left font-normal text-muted-foreground">{option.tip}</p>
                            </ToggleGroupItem>
                        ))}
                    </ToggleGroup>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Étape 2 : De quoi je parle ?</CardTitle>
                    <CardDescription>Sélectionnez un ou plusieurs thèmes qui serviront de fil conducteur.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    {groupedThemes.map(group => (
                        <div key={group.label}>
                            <h4 className="font-semibold text-muted-foreground mb-2">{group.label}</h4>
                            <div className="flex flex-wrap gap-1.5">
                                {group.themes.map(theme => (
                                    <Badge 
                                        key={theme.id}
                                        onClick={() => setThemeIds(prev => prev.includes(theme.id) ? prev.filter(id => id !== theme.id) : [...prev, theme.id])}
                                        variant={themeIds.includes(theme.id) ? "default" : "secondary"}
                                        className="cursor-pointer py-1"
                                    >
                                        <theme.icon className="w-3.5 h-3.5 mr-1.5"/>
                                        {theme.title}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    ))}
                </CardContent>
            </Card>
        
            <Card>
                <CardHeader>
                    <CardTitle>Étape 3 : Pourquoi j’en parle ? (Construire le programme)</CardTitle>
                    <CardDescription>Sélectionnez les fiches-objectifs pour construire vos séances.</CardDescription>
                </CardHeader>
                <CardContent>
                     <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                        <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="explore">Explorer les fiches ({filteredAvailableCards.length})</TabsTrigger>
                            <TabsTrigger value="selected">Mon Programme ({items.selected.length})</TabsTrigger>
                        </TabsList>
                        <TabsContent value="explore" className="mt-4">
                           <div className="flex gap-2 mb-4">
                                <ToggleGroup type="single" value={pillarFilter ?? ""} onValueChange={(value) => setPillarFilter(value || null)}>
                                    {Object.entries(PILLAR_STYLES).map(([key, { filterBadge, icon: PillarIcon, text }]) => (
                                        <ToggleGroupItem key={key} value={key} className={cn("border", pillarFilter === key && filterBadge)}>
                                            <PillarIcon className={cn("w-4 h-4 mr-2", pillarFilter === key ? text : 'text-muted-foreground')} />
                                            {key.charAt(0).toUpperCase() + key.slice(1)}
                                        </ToggleGroupItem>
                                    ))}
                                </ToggleGroup>
                                {pillarFilter && <Button variant="ghost" size="sm" onClick={() => setPillarFilter(null)}>Effacer</Button>}
                            </div>

                            <div className="min-h-[400px] max-h-[60vh] overflow-y-auto bg-muted/50 p-2 rounded-lg space-y-2">
                                {filteredAvailableCards.length > 0 ? (
                                    filteredAvailableCards.map(card => (
                                        <div key={card.id} className={cn("transition-opacity", pillarFilter && pillarFilter !== card.option_id && "opacity-40 hover:opacity-100")}>
                                            <ActionableCard card={card} onAdd={() => addCard(card)} />
                                        </div>
                                    ))
                                ) : (
                                    <p className="p-4 text-center text-sm text-muted-foreground">Aucune fiche à afficher. Essayez de changer les filtres ou le niveau du stage.</p>
                                )}
                            </div>
                        </TabsContent>
                        <TabsContent value="selected" className="mt-4">
                             <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                                <div className="min-h-[400px] max-h-[60vh] overflow-y-auto bg-muted/50 p-2 rounded-lg space-y-2">
                                    <SortableContext items={items.selected} strategy={verticalListSortingStrategy}>
                                        {items.selected.length > 0 ? (
                                            items.selected.map(card => (
                                                <DraggableCard key={card.id} card={card} onRemove={() => removeCard(card)} />
                                            ))
                                        ) : (
                                            <p className="p-4 text-center text-sm text-muted-foreground">Cliquez sur les fiches dans l'onglet "Explorer" pour les ajouter ici.</p>
                                        )}
                                    </SortableContext>
                                </div>
                            </DndContext>
                        </TabsContent>
                    </Tabs>
                </CardContent>
            </Card>
        
         <Card>
            <CardHeader><CardTitle>Étape 4 : Action de l’encadrant (Finalisation)</CardTitle></CardHeader>
            <CardContent className="flex flex-col sm:flex-row gap-4">
                <Button asChild className="w-full" disabled={items.selected.length === 0}>
                    <Link href={`/jeux/validation?stageId=${stage.id}&objectives=${items.selected.map(c => c.id.replace('q','')).join(',')}`}>
                        <GraduationCap className="mr-2 h-4 w-4" />Je teste mes connaissances
                    </Link>
                </Button>
                <Button className="w-full" onClick={handleSaveProgram} disabled={isSaving}>
                    {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin"/> : <Save className="mr-2 h-4 w-4" />}
                    Appliquer le programme
                </Button>
            </CardContent>
        </Card>
      </div>
    );
};


const ActionableCard = ({ card, onAdd }: { card: ContentCard, onAdd: () => void }) => {
    const pillar = card.option_id as keyof typeof PILLAR_STYLES;
    const styleInfo = PILLAR_STYLES[pillar] || {};

    return (
        <div className={cn("p-2 bg-card border-l-4 flex items-center gap-2 rounded-md shadow-sm", styleInfo.border)}>
            <div className="flex-grow">
                <p className="text-sm font-semibold">{card.question}</p>
                <p className="text-xs text-muted-foreground">{card.answer}</p>
            </div>
            <Badge className={cn(styleInfo.badge, "pointer-events-none")}>{pillar}</Badge>
            <Button size="icon" variant="ghost" onClick={onAdd} className="h-8 w-8 shrink-0">
                <Plus className="h-4 w-4" />
            </Button>
        </div>
    );
}

const DraggableCard = ({ card, onRemove }: { card: ContentCard, onRemove: () => void }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({id: card.id});
    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        opacity: isDragging ? 0.5 : 1,
        zIndex: isDragging ? 10 : 'auto',
    };
    const pillar = card.option_id as keyof typeof PILLAR_STYLES;
    const styleInfo = PILLAR_STYLES[pillar] || {};

    return (
        <div ref={setNodeRef} style={style} className={cn("p-2 bg-card border-l-4 flex items-center gap-2 rounded-md shadow-sm", styleInfo.border)}>
            <span {...listeners} {...attributes} className="cursor-grab active:cursor-grabbing p-1">
                <GripVertical className="h-5 w-5 text-muted-foreground" />
            </span>
            <div className="flex-grow">
              <p className="text-sm font-semibold">{card.question}</p>
              <p className="text-xs text-muted-foreground">{card.answer}</p>
            </div>
            <Badge className={cn(styleInfo.badge, "pointer-events-none")}>{pillar}</Badge>
            <Button size="icon" variant="ghost" onClick={onRemove} className="h-8 w-8 shrink-0 text-muted-foreground hover:text-foreground">
                <Undo2 className="h-4 w-4" />
            </Button>
        </div>
    );
};

const GamesView = ({ games, stageId }: { games: Game[], stageId: number }) => {
    const newGameURL = `/jeux/generateur?stageId=${stageId}`;
    return (
        <Card>
            <CardHeader>
                <div className="flex justify-between items-center">
                    <div><CardTitle>Jeux de Révision</CardTitle><CardDescription>Créez et lancez des jeux pour réviser les notions.</CardDescription></div>
                    <Link href={newGameURL}><Button><PlusCircle className="mr-2 h-4 w-4" />Créer un jeu</Button></Link>
                </div>
            </CardHeader>
            <CardContent>
                {games.length > 0 ? (
                    <div className="space-y-3">{games.map((game: Game) => (
                        <Card key={game.id} className="hover:bg-muted transition-colors group relative">
                            <Link href={`/jeux/${game.id}`}><CardContent className="p-4 flex justify-between items-center">
                                <div><p className="font-semibold">{game.title}</p><p className="text-sm text-muted-foreground">Thème: {game.theme} - Créé le {format(new Date(game.created_at), 'd MMM yyyy', { locale: fr })}</p></div>
                                <div className="flex items-center gap-2"><Gamepad2 className="w-5 h-5 text-muted-foreground"/><ArrowRight className="w-4 h-4 text-muted-foreground" /></div>
                            </CardContent></Link>
                        </Card>
                    ))}</div>
                ) : (
                    <div className="text-center py-10 px-4 border-2 border-dashed rounded-lg"><h3 className="text-lg font-semibold">Aucun jeu créé</h3><p className="text-muted-foreground mt-1">Créez votre premier jeu pour ce stage.</p></div>
                )}
            </CardContent>
        </Card>
    );
};

const ValidationSlider = ({ isCompleted, onToggle }: { isCompleted: boolean; onToggle: () => void }) => {
    const x = useMotionValue(0);

    useEffect(() => {
        x.set(0);
    }, [isCompleted, x]);

    const sliderWidth = 160; 
    const thumbWidth = 40; 
    const pathLength = sliderWidth - thumbWidth;

    const textOpacity = useTransform(x, [0, pathLength * 0.3], [1, 0]);
    const checkOpacity = useTransform(x, [pathLength * 0.7, pathLength], [0, 1]);
    
    const handleDragEnd = (event: any, info: any) => {
        if (info.offset.x > pathLength * 0.7) {
            onToggle();
        }
        x.set(0);
    };

    if (isCompleted) {
        return (
             <Button
                variant="ghost"
                size="sm"
                onClick={onToggle}
                className="text-xs h-8 px-2 text-green-500 hover:text-green-400"
            >
                <CheckCircle className="w-4 h-4 mr-1" />
               Notion vue
            </Button>
        );
    }
    
    return (
        <div
            className="relative w-40 h-8 bg-secondary rounded-full flex items-center justify-center p-1 overflow-hidden"
        >
            <motion.span 
                className="text-xs font-medium text-muted-foreground z-0 select-none"
                style={{ opacity: textOpacity }}
            >
                Glisser pour valider
            </motion.span>
            
            <motion.div
                drag="x"
                dragConstraints={{ left: 0, right: pathLength }}
                style={{ x }}
                onDragEnd={handleDragEnd}
                className="absolute top-1 left-1 h-6 w-6 rounded-full bg-background shadow flex items-center justify-center z-10 cursor-grab active:cursor-grabbing"
                dragMomentum={false}
                dragElastic={0.1}
            >
                <motion.div style={{ opacity: checkOpacity }} className="text-green-500">
                    <Check className="w-4 h-4" />
                </motion.div>
                <motion.div style={{ opacity: textOpacity }} className="absolute text-muted-foreground">
                    <ChevronRightIcon className="w-4 h-4" />
                </motion.div>
            </motion.div>
        </div>
    );
};

export const dynamic = 'force-dynamic';







