(()=>{var e={};e.id=444,e.ids=[444],e.modules={2327:()=>{},2727:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,88416,23)),Promise.resolve().then(n.t.bind(n,27342,23)),Promise.resolve().then(n.t.bind(n,74078,23)),Promise.resolve().then(n.t.bind(n,64193,23)),Promise.resolve().then(n.t.bind(n,91573,23)),Promise.resolve().then(n.t.bind(n,95405,23)),Promise.resolve().then(n.t.bind(n,97301,23)),Promise.resolve().then(n.t.bind(n,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11107:(e,t,n)=>{"use strict";var r=n(96575);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},11654:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=n(5853),o=n(60554),i=n(30708),a=n.n(i),l=n(8067),s={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>l[e]);n.d(t,s);let c={children:["",{children:["stages",{children:["[stageId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,58835)),"C:\\PL\\COPUN-V5\\src\\app\\stages\\[stageId]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\PL\\COPUN-V5\\src\\app\\stages\\[stageId]\\page.tsx"],u={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/stages/[stageId]/page",pathname:"/stages/[stageId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},14735:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]])},15992:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20625:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ListFilter",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M7 12h10",key:"b7w52i"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},24870:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31077:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>o});var r=n(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33284:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Gamepad2",[["line",{x1:"6",x2:"10",y1:"11",y2:"11",key:"1gktln"}],["line",{x1:"8",x2:"8",y1:"9",y2:"13",key:"qnk9ow"}],["line",{x1:"15",x2:"15.01",y1:"12",y2:"12",key:"krot7o"}],["line",{x1:"18",x2:"18.01",y1:"10",y2:"10",key:"1lcuu1"}],["path",{d:"M17.32 5H6.68a4 4 0 0 0-3.978 3.59c-.006.052-.01.101-.017.152C2.604 9.416 2 14.456 2 16a3 3 0 0 0 3 3c1 0 1.5-.5 2-1l1.414-1.414A2 2 0 0 1 9.828 16h4.344a2 2 0 0 1 1.414.586L17 18c.5.5 1 1 2 1a3 3 0 0 0 3-3c0-1.545-.604-6.584-.685-7.258-.007-.05-.011-.1-.017-.151A4 4 0 0 0 17.32 5z",key:"mfqc10"}]])},33873:e=>{"use strict";e.exports=require("path")},39103:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},43596:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Trophy",[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]])},47273:(e,t,n)=>{"use strict";let r;n.r(t),n.d(t,{default:()=>nr});var o,i,a,l,s,c,d,u,h,f,m=n(28625),p=n(64996),g=n.n(p),v=n(11107),b=n(93948),x=n.n(b),O=n(58709);let w="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function N(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function y(e){return"nodeType"in e}function D(e){var t,n;return e?N(e)?e:y(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function j(e){let{Document:t}=D(e);return e instanceof t}function E(e){return!N(e)&&e instanceof D(e).HTMLElement}function C(e){return e instanceof D(e).SVGElement}function _(e){return e?N(e)?e.document:y(e)?j(e)?e:E(e)||C(e)?e.ownerDocument:document:document:document}let U=w?p.useLayoutEffect:p.useEffect;function M(e){let t=(0,p.useRef)(e);return U(()=>{t.current=e}),(0,p.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function T(e,t){void 0===t&&(t=[e]);let n=(0,p.useRef)(e);return U(()=>{n.current!==e&&(n.current=e)},t),n}function L(e,t){let n=(0,p.useRef)();return(0,p.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function k(e){let t=M(e),n=(0,p.useRef)(null),r=(0,p.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,r]}function F(e){let t=(0,p.useRef)();return(0,p.useEffect)(()=>{t.current=e},[e]),t.current}let S={};function A(e,t){return(0,p.useMemo)(()=>{if(t)return t;let n=null==S[e]?0:S[e]+1;return S[e]=n,e+"-"+n},[e,t])}function P(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((t,n)=>{for(let[r,o]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*o)}return t},{...t})}}let R=P(1),I=P(-1);function z(e){if(!e)return!1;let{KeyboardEvent:t}=D(e.target);return t&&e instanceof t}function q(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=D(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let B=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[B.Translate.toString(e),B.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),V="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",H={display:"none"};function Y(e){let{id:t,value:n}=e;return g().createElement("div",{id:t,style:H},n)}function G(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return g().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let X=(0,p.createContext)(null),K={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},W={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function $(e){let{announcements:t=W,container:n,hiddenTextDescribedById:r,screenReaderInstructions:o=K}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),l=A("DndLiveRegion"),[s,c]=(0,p.useState)(!1);(0,p.useEffect)(()=>{c(!0)},[]);var d=(0,p.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t]);let u=(0,p.useContext)(X);if((0,p.useEffect)(()=>{if(!u)throw Error("useDndMonitor must be used within a children of <DndContext>");return u(d)},[d,u]),!s)return null;let h=g().createElement(g().Fragment,null,g().createElement(Y,{id:r,value:o.draggable}),g().createElement(G,{id:l,announcement:a}));return n?(0,O.createPortal)(h,n):h}function J(){}function Z(e,t){return(0,p.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(o||(o={}));let Q=Object.freeze({x:0,y:0});function ee(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function et(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function en(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function er(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function eo(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function ei(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let ea=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,o=ei(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=ee(ei(r),o);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(et)},el=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,o=er(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=er(r),a=Number((o.reduce((e,t,r)=>e+ee(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(et)},es=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,o=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<o&&n<i){let a=t.width*t.height,l=e.width*e.height,s=(o-r)*(i-n);return Number((s/(a+l-s)).toFixed(4))}return 0}(i,t);n>0&&o.push({id:r,data:{droppableContainer:e,value:n}})}}return o.sort(en)};function ec(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:Q}let ed=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),eu={ignoreTransform:!1};function eh(e,t){void 0===t&&(t=eu);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=D(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),d=o?e.width/o:e.width,u=i?e.height/i:e.height;return{width:d,height:u,top:c,right:s+d,bottom:c+u,left:s}}(n,t,r))}let{top:r,left:o,width:i,height:a,bottom:l,right:s}=n;return{top:r,left:o,width:i,height:a,bottom:l,right:s}}function ef(e){return eh(e,{ignoreTransform:!0})}function em(e,t){let n=[];return e?function r(o){var i;if(null!=t&&n.length>=t||!o)return n;if(j(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!E(o)||C(o)||n.includes(o))return n;let a=D(e).getComputedStyle(o);return(o!==e&&function(e,t){void 0===t&&(t=D(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(o,a)&&n.push(o),void 0===(i=a)&&(i=D(o).getComputedStyle(o)),"fixed"===i.position)?n:r(o.parentNode)}(e):n}function ep(e){let[t]=em(e,1);return null!=t?t:null}function eg(e){return w&&e?N(e)?e:y(e)?j(e)||e===_(e).scrollingElement?window:E(e)?e:null:null:null}function ev(e){return N(e)?e.scrollX:e.scrollLeft}function eb(e){return N(e)?e.scrollY:e.scrollTop}function ex(e){return{x:ev(e),y:eb(e)}}function eO(e){return!!w&&!!e&&e===document.scrollingElement}function ew(e){let t={x:0,y:0},n=eO(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},o=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:o,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let eN={x:.2,y:.2};function ey(e){return e.reduce((e,t)=>R(e,ex(t)),Q)}let eD=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+ev(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+eb(t),0)}]];class ej{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=em(t),r=ey(n);for(let[t,o,i]of(this.rect={...e},this.width=e.width,this.height=e.height,eD))for(let e of o)Object.defineProperty(this,e,{get:()=>{let o=i(n),a=r[t]-o;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class eE{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function eC(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function e_(e){e.preventDefault()}function eU(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(l||(l={}));let eM={start:[l.Space,l.Enter],cancel:[l.Esc],end:[l.Space,l.Enter,l.Tab]},eT=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case l.Right:return{...n,x:n.x+25};case l.Left:return{...n,x:n.x-25};case l.Down:return{...n,y:n.y+25};case l.Up:return{...n,y:n.y-25}}};class eL{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new eE(_(t)),this.windowListeners=new eE(D(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=eh),!e)return;let{top:n,left:r,bottom:o,right:i}=t(e);ep(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(Q)}handleKeyDown(e){if(z(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:o=eM,coordinateGetter:i=eT,scrollBehavior:a="smooth"}=r,{code:s}=e;if(o.end.includes(s))return void this.handleEnd(e);if(o.cancel.includes(s))return void this.handleCancel(e);let{collisionRect:c}=n.current,d=c?{x:c.left,y:c.top}:Q;this.referenceCoordinates||(this.referenceCoordinates=d);let u=i(e,{active:t,context:n.current,currentCoordinates:d});if(u){let t=I(u,d),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(let n of o){let o=e.code,{isTop:i,isRight:s,isLeft:c,isBottom:d,maxScroll:h,minScroll:f}=ew(n),m=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}(n),p={x:Math.min(o===l.Right?m.right-m.width/2:m.right,Math.max(o===l.Right?m.left:m.left+m.width/2,u.x)),y:Math.min(o===l.Down?m.bottom-m.height/2:m.bottom,Math.max(o===l.Down?m.top:m.top+m.height/2,u.y))},g=o===l.Right&&!s||o===l.Left&&!c,v=o===l.Down&&!d||o===l.Up&&!i;if(g&&p.x!==u.x){let e=n.scrollLeft+t.x,i=o===l.Right&&e<=h.x||o===l.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});i?r.x=n.scrollLeft-e:r.x=o===l.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(v&&p.y!==u.y){let e=n.scrollTop+t.y,i=o===l.Down&&e<=h.y||o===l.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});i?r.y=n.scrollTop-e:r.y=o===l.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,R(I(u,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ek(e){return!!(e&&"distance"in e)}function eF(e){return!!(e&&"delay"in e)}eL.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=eM,onActivation:o}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==o||o({event:e.nativeEvent}),!0)}return!1}}];class eS{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=D(e);return e instanceof t?e:_(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:o}=e,{target:i}=o;this.props=e,this.events=t,this.document=_(i),this.documentListeners=new eE(this.document),this.listeners=new eE(n),this.windowListeners=new eE(D(i)),this.initialCoordinates=null!=(r=q(o))?r:Q,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,e_),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,e_),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eF(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(ek(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,eU,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:o}=this,{onMove:i,options:{activationConstraint:a}}=o;if(!r)return;let l=null!=(t=q(e))?t:Q,s=I(r,l);if(!n&&a){if(ek(a)){if(null!=a.tolerance&&eC(s,a.tolerance))return this.handleCancel();if(eC(s,a.distance))return this.handleStart()}return eF(a)&&eC(s,a.tolerance)?this.handleCancel():void this.handlePending(a,s)}e.cancelable&&e.preventDefault(),i(l)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===l.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let eA={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class eP extends eS{constructor(e){let{event:t}=e;super(e,eA,_(t.target))}}eP.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eR={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(s||(s={}));class eI extends eS{constructor(e){super(e,eR,_(e.event.target))}}eI.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==s.RightClick&&(null==r||r({event:n}),!0)}}];let ez={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eq extends eS{constructor(e){super(e,ez)}static setup(){return window.addEventListener(ez.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ez.move.name,e)};function e(){}}}eq.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:o}=n;return!(o.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(c||(c={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(d||(d={}));let eB={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(u||(u={})),(h||(h={})).Optimized="optimized";let eV=new Map;function eH(e,t){return L(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function eY(e){let{callback:t,disabled:n}=e,r=M(t),o=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,p.useEffect)(()=>()=>null==o?void 0:o.disconnect(),[o]),o}function eG(e){return new ej(eh(e),e)}function eX(e,t,n){void 0===t&&(t=eG);let[r,o]=(0,p.useState)(null);function i(){o(r=>{if(!e)return null;if(!1===e.isConnected){var o;return null!=(o=null!=r?r:n)?o:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=M(t),o=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,p.useEffect)(()=>()=>null==o?void 0:o.disconnect(),[o]),o}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),l=eY({callback:i});return U(()=>{i(),e?(null==l||l.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==l||l.disconnect(),null==a||a.disconnect())},[e]),r}let eK=[];function eW(e,t){void 0===t&&(t=[]);let n=(0,p.useRef)(null);return(0,p.useEffect)(()=>{n.current=null},t),(0,p.useEffect)(()=>{let t=e!==Q;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?I(e,n.current):Q}function e$(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eJ=[],eZ=[{sensor:eP,options:{}},{sensor:eL,options:{}}],eQ={current:{}},e0={draggable:{measure:ef},droppable:{measure:ef,strategy:u.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:eh}};class e1 extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let e2={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new e1,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:J},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:e0,measureDroppableContainers:J,windowRect:null,measuringScheduled:!1},e4={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:J,draggableNodes:new Map,over:null,measureDroppableContainers:J},e5=(0,p.createContext)(e4),e3=(0,p.createContext)(e2);function e7(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new e1}}}function e8(e,t){switch(t.type){case o.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case o.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case o.DragEnd:case o.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case o.RegisterDroppable:{let{element:n}=t,{id:r}=n,o=new e1(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case o.SetDroppableDisabled:{let{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new e1(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case o.UnregisterDroppable:{let{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;let i=new e1(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function e9(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:o}=(0,p.useContext)(e5),i=F(r),a=F(null==n?void 0:n.id);return(0,p.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!z(i)||document.activeElement===i.target)return;let e=o.get(a);if(!e)return;let{activatorNode:t,node:n}=e;(t.current||n.current)&&requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=e.matches(V)?e:e.querySelector(V);if(t){t.focus();break}}})}},[r,t,o,a,i]),null}let e6=(0,p.createContext)({...Q,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let te=(0,p.memo)(function(e){var t,n,r,a,l,s;let{id:h,accessibility:m,autoScroll:v=!0,children:b,sensors:x=eZ,collisionDetection:N=es,measuring:y,modifiers:j,...C}=e,[_,M]=(0,p.useReducer)(e8,void 0,e7),[S,P]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[I,z]=(0,p.useState)(f.Uninitialized),B=I===f.Initialized,{draggable:{active:V,nodes:H,translate:Y},droppable:{containers:G}}=_,K=null!=V?H.get(V):null,W=(0,p.useRef)({initial:null,translated:null}),J=(0,p.useMemo)(()=>{var e;return null!=V?{id:V,data:null!=(e=null==K?void 0:K.data)?e:eQ,rect:W}:null},[V,K]),Z=(0,p.useRef)(null),[ee,et]=(0,p.useState)(null),[en,er]=(0,p.useState)(null),ei=T(C,Object.values(C)),ea=A("DndDescribedBy",h),el=(0,p.useMemo)(()=>G.getEnabled(),[G]),eu=(0,p.useMemo)(()=>({draggable:{...e0.draggable,...null==y?void 0:y.draggable},droppable:{...e0.droppable,...null==y?void 0:y.droppable},dragOverlay:{...e0.dragOverlay,...null==y?void 0:y.dragOverlay}}),[null==y?void 0:y.draggable,null==y?void 0:y.droppable,null==y?void 0:y.dragOverlay]),{droppableRects:ef,measureDroppableContainers:ev,measuringScheduled:eb}=function(e,t){let{dragging:n,dependencies:r,config:o}=t,[i,a]=(0,p.useState)(null),{frequency:l,measure:s,strategy:c}=o,d=(0,p.useRef)(e),h=function(){switch(c){case u.Always:return!1;case u.BeforeDragging:return n;default:return!n}}(),f=T(h),m=(0,p.useCallback)(function(e){void 0===e&&(e=[]),f.current||a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),g=(0,p.useRef)(null),v=L(t=>{if(h&&!n)return eV;if(!t||t===eV||d.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new ej(s(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,h,s]);return(0,p.useEffect)(()=>{d.current=e},[e]),(0,p.useEffect)(()=>{h||m()},[n,h]),(0,p.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,p.useEffect)(()=>{h||"number"!=typeof l||null!==g.current||(g.current=setTimeout(()=>{m(),g.current=null},l))},[l,h,m,...r]),{droppableRects:v,measureDroppableContainers:m,measuringScheduled:null!=i}}(el,{dragging:B,dependencies:[Y.x,Y.y],config:eu.droppable}),eD=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return L(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(H,V),eE=(0,p.useMemo)(()=>en?q(en):null,[en]),eC=function(){let e=(null==ee?void 0:ee.autoScrollEnabled)===!1,t="object"==typeof v?!1===v.enabled:!1===v,n=B&&!e&&!t;return"object"==typeof v?{...v,enabled:n}:{enabled:n}}(),e_=eH(eD,eu.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:o=!0}=e,i=(0,p.useRef)(!1),{x:a,y:l}="boolean"==typeof o?{x:o,y:o}:o;U(()=>{if(!a&&!l||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let o=ec(n(e),r);if(a||(o.x=0),l||(o.y=0),i.current=!0,Math.abs(o.x)>0||Math.abs(o.y)>0){let t=ep(e);t&&t.scrollBy({top:o.y,left:o.x})}},[t,a,l,r,n])}({activeNode:null!=V?H.get(V):null,config:eC.layoutShiftCompensation,initialRect:e_,measure:eu.draggable.measure});let eU=eX(eD,eu.draggable.measure,e_),eM=eX(eD?eD.parentElement:null),eT=(0,p.useRef)({activatorEvent:null,active:null,activeNode:eD,collisionRect:null,collisions:null,droppableRects:ef,draggableNodes:H,draggingNode:null,draggingNodeRect:null,droppableContainers:G,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eL=G.getNodeFor(null==(t=eT.current.over)?void 0:t.id),ek=function(e){let{measure:t}=e,[n,r]=(0,p.useState)(null),o=eY({callback:(0,p.useCallback)(e=>{for(let{target:n}of e)if(E(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),[i,a]=k((0,p.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return E(t)?t:e}(e);null==o||o.disconnect(),n&&(null==o||o.observe(n)),r(n?t(n):null)},[t,o]));return(0,p.useMemo)(()=>({nodeRef:i,rect:n,setRef:a}),[n,i,a])}({measure:eu.dragOverlay.measure}),eF=null!=(n=ek.nodeRef.current)?n:eD,eS=B?null!=(r=ek.rect)?r:eU:null,eA=!!(ek.nodeRef.current&&ek.rect),eP=function(e){let t=eH(e);return ec(e,t)}(eA?null:eU),eR=e$(eF?D(eF):null),eI=function(e){let t=(0,p.useRef)(e),n=L(n=>e?n&&n!==eK&&e&&t.current&&e.parentNode===t.current.parentNode?n:em(e):eK,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),n}(B?null!=eL?eL:eD:null),ez=function(e,t){void 0===t&&(t=eh);let[n]=e,r=e$(n?D(n):null),[o,i]=(0,p.useState)(eJ);function a(){i(()=>e.length?e.map(e=>eO(e)?r:new ej(t(e),e)):eJ)}let l=eY({callback:a});return U(()=>{null==l||l.disconnect(),a(),e.forEach(e=>null==l?void 0:l.observe(e))},[e]),o}(eI),eq=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(j,{transform:{x:Y.x-eP.x,y:Y.y-eP.y,scaleX:1,scaleY:1},activatorEvent:en,active:J,activeNodeRect:eU,containerNodeRect:eM,draggingNodeRect:eS,over:eT.current.over,overlayNodeRect:ek.rect,scrollableAncestors:eI,scrollableAncestorRects:ez,windowRect:eR}),eG=eE?R(eE,Y):null,e1=function(e){let[t,n]=(0,p.useState)(null),r=(0,p.useRef)(e),o=(0,p.useCallback)(e=>{let t=eg(e.target);t&&n(e=>e?(e.set(t,ex(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=eg(e);return t?(t.addEventListener("scroll",o,{passive:!0}),[t,ex(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=eg(e);null==t||t.removeEventListener("scroll",o)})}},[o,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>R(e,t),Q):ey(e):Q,[e,t])}(eI),e2=eW(e1),e4=eW(e1,[eU]),te=R(eq,e2),tt=eS?ed(eS,eq):null,tn=J&&tt?N({active:J,collisionRect:tt,droppableRects:ef,droppableContainers:el,pointerCoordinates:eG}):null,tr=eo(tn,"id"),[to,ti]=(0,p.useState)(null),ta=(l=eA?eq:R(eq,e4),s=null!=(a=null==to?void 0:to.rect)?a:null,{...l,scaleX:s&&eU?s.width/eU.width:1,scaleY:s&&eU?s.height/eU.height:1}),tl=(0,p.useRef)(null),ts=(0,p.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==Z.current)return;let i=H.get(Z.current);if(!i)return;let a=e.nativeEvent,l=new n({active:Z.current,activeNode:i,event:a,options:r,context:eT,onAbort(e){if(!H.get(e))return;let{onDragAbort:t}=ei.current,n={id:e};null==t||t(n),S({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!H.get(e))return;let{onDragPending:o}=ei.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==o||o(i),S({type:"onDragPending",event:i})},onStart(e){let t=Z.current;if(null==t)return;let n=H.get(t);if(!n)return;let{onDragStart:r}=ei.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:W}};(0,O.unstable_batchedUpdates)(()=>{null==r||r(i),z(f.Initializing),M({type:o.DragStart,initialCoordinates:e,active:t}),S({type:"onDragStart",event:i}),et(tl.current),er(a)})},onMove(e){M({type:o.DragMove,coordinates:e})},onEnd:s(o.DragEnd),onCancel:s(o.DragCancel)});function s(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eT.current,l=null;if(t&&i){let{cancelDrop:s}=ei.current;l={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===o.DragEnd&&"function"==typeof s&&await Promise.resolve(s(l))&&(e=o.DragCancel)}Z.current=null,(0,O.unstable_batchedUpdates)(()=>{M({type:e}),z(f.Uninitialized),ti(null),et(null),er(null),tl.current=null;let t=e===o.DragEnd?"onDragEnd":"onDragCancel";if(l){let e=ei.current[t];null==e||e(l),S({type:t,event:l})}})}}tl.current=l},[H]),tc=(0,p.useCallback)((e,t)=>(n,r)=>{let o=n.nativeEvent,i=H.get(r);null!==Z.current||!i||o.dndKit||o.defaultPrevented||!0===e(n,t.options,{active:i})&&(o.dndKit={capturedBy:t.sensor},Z.current=r,ts(n,t))},[H,ts]),td=(0,p.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:tc(e.handler,t)}))]},[]),[x,tc]);(0,p.useEffect)(()=>{if(!w)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),U(()=>{eU&&I===f.Initializing&&z(f.Initialized)},[eU,I]),(0,p.useEffect)(()=>{let{onDragMove:e}=ei.current,{active:t,activatorEvent:n,collisions:r,over:o}=eT.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:te.x,y:te.y},over:o};(0,O.unstable_batchedUpdates)(()=>{null==e||e(i),S({type:"onDragMove",event:i})})},[te.x,te.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=eT.current;if(!e||null==Z.current||!t||!o)return;let{onDragOver:i}=ei.current,a=r.get(tr),l=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,s={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:l};(0,O.unstable_batchedUpdates)(()=>{ti(l),null==i||i(s),S({type:"onDragOver",event:s})})},[tr]),U(()=>{eT.current={activatorEvent:en,active:J,activeNode:eD,collisionRect:tt,collisions:tn,droppableRects:ef,draggableNodes:H,draggingNode:eF,draggingNodeRect:eS,droppableContainers:G,over:to,scrollableAncestors:eI,scrollAdjustedTranslate:te},W.current={initial:eS,translated:tt}},[J,eD,tn,tt,H,eF,eS,ef,G,to,eI,te]),function(e){let{acceleration:t,activator:n=c.Pointer,canScroll:r,draggingRect:o,enabled:a,interval:l=5,order:s=d.TreeOrder,pointerCoordinates:u,scrollableAncestors:h,scrollableAncestorRects:f,delta:m,threshold:g}=e,v=function(e){let{delta:t,disabled:n}=e,r=F(t);return L(e=>{if(n||!r||!e)return eB;let o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===o.x,[i.Forward]:e.x[i.Forward]||1===o.x},y:{[i.Backward]:e.y[i.Backward]||-1===o.y,[i.Forward]:e.y[i.Forward]||1===o.y}}},[n,t,r])}({delta:m,disabled:!a}),[b,x]=function(){let e=(0,p.useRef)(null);return[(0,p.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,p.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),O=(0,p.useRef)({x:0,y:0}),w=(0,p.useRef)({x:0,y:0}),N=(0,p.useMemo)(()=>{switch(n){case c.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case c.DraggableRect:return o}},[n,o,u]),y=(0,p.useRef)(null),D=(0,p.useCallback)(()=>{let e=y.current;if(!e)return;let t=O.current.x*w.current.x,n=O.current.y*w.current.y;e.scrollBy(t,n)},[]),j=(0,p.useMemo)(()=>s===d.TreeOrder?[...h].reverse():h,[s,h]);(0,p.useEffect)(()=>{if(!a||!h.length||!N)return void x();for(let e of j){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:o,speed:a}=function(e,t,n,r,o){let{top:a,left:l,right:s,bottom:c}=n;void 0===r&&(r=10),void 0===o&&(o=eN);let{isTop:d,isBottom:u,isLeft:h,isRight:f}=ew(e),m={x:0,y:0},p={x:0,y:0},g={height:t.height*o.y,width:t.width*o.x};return!d&&a<=t.top+g.height?(m.y=i.Backward,p.y=r*Math.abs((t.top+g.height-a)/g.height)):!u&&c>=t.bottom-g.height&&(m.y=i.Forward,p.y=r*Math.abs((t.bottom-g.height-c)/g.height)),!f&&s>=t.right-g.width?(m.x=i.Forward,p.x=r*Math.abs((t.right-g.width-s)/g.width)):!h&&l<=t.left+g.width&&(m.x=i.Backward,p.x=r*Math.abs((t.left+g.width-l)/g.width)),{direction:m,speed:p}}(e,n,N,t,g);for(let e of["x","y"])v[e][o[e]]||(a[e]=0,o[e]=0);if(a.x>0||a.y>0){x(),y.current=e,b(D,l),O.current=a,w.current=o;return}}O.current={x:0,y:0},w.current={x:0,y:0},x()},[t,D,r,x,a,l,JSON.stringify(N),JSON.stringify(v),b,h,j,f,JSON.stringify(g)])}({...eC,delta:Y,draggingRect:tt,pointerCoordinates:eG,scrollableAncestors:eI,scrollableAncestorRects:ez});let tu=(0,p.useMemo)(()=>({active:J,activeNode:eD,activeNodeRect:eU,activatorEvent:en,collisions:tn,containerNodeRect:eM,dragOverlay:ek,draggableNodes:H,droppableContainers:G,droppableRects:ef,over:to,measureDroppableContainers:ev,scrollableAncestors:eI,scrollableAncestorRects:ez,measuringConfiguration:eu,measuringScheduled:eb,windowRect:eR}),[J,eD,eU,en,tn,eM,ek,H,G,ef,to,ev,eI,ez,eu,eb,eR]),th=(0,p.useMemo)(()=>({activatorEvent:en,activators:td,active:J,activeNodeRect:eU,ariaDescribedById:{draggable:ea},dispatch:M,draggableNodes:H,over:to,measureDroppableContainers:ev}),[en,td,J,eU,M,ea,H,to,ev]);return g().createElement(X.Provider,{value:P},g().createElement(e5.Provider,{value:th},g().createElement(e3.Provider,{value:tu},g().createElement(e6.Provider,{value:ta},b)),g().createElement(e9,{disabled:(null==m?void 0:m.restoreFocus)===!1})),g().createElement($,{...m,hiddenTextDescribedById:ea}))}),tt=(0,p.createContext)(null),tn="button",tr={timeout:25};r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,o={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(o[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(o))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}};function to(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function ti(e){return null!==e&&e>=0}let ta=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e,i=to(t,r,n),a=t[o],l=i[o];return l&&a?{x:l.left-a.left,y:l.top-a.top,scaleX:l.width/a.width,scaleY:l.height/a.height}:null},tl={scaleX:1,scaleY:1},ts=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:i,overIndex:a}=e,l=null!=(t=i[n])?t:r;if(!l)return null;if(o===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(l.top+l.height):e.top-l.top,...tl}:null}let s=function(e,t,n){let r=e[t],o=e[t-1],i=e[t+1];return r?n<t?o?r.top-(o.top+o.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):o?r.top-(o.top+o.height):0:0}(i,o,n);return o>n&&o<=a?{x:0,y:-l.height-s,...tl}:o<n&&o>=a?{x:0,y:l.height+s,...tl}:{x:0,y:0,...tl}},tc="Sortable",td=g().createContext({activeIndex:-1,containerId:tc,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:ta,disabled:{draggable:!1,droppable:!1}});function tu(e){let{children:t,id:n,items:r,strategy:o=ta,disabled:i=!1}=e,{active:a,dragOverlay:l,droppableRects:s,over:c,measureDroppableContainers:d}=(0,p.useContext)(e3),u=A(tc,n),h=null!==l.rect,f=(0,p.useMemo)(()=>r.map(e=>"object"==typeof e&&"id"in e?e.id:e),[r]),m=null!=a,v=a?f.indexOf(a.id):-1,b=c?f.indexOf(c.id):-1,x=(0,p.useRef)(f),O=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(f,x.current),w=-1!==b&&-1===v||O,N="boolean"==typeof i?{draggable:i,droppable:i}:i;U(()=>{O&&m&&d(f)},[O,f,m,d]),(0,p.useEffect)(()=>{x.current=f},[f]);let y=(0,p.useMemo)(()=>({activeIndex:v,containerId:u,disabled:N,disableTransforms:w,items:f,overIndex:b,useDragOverlay:h,sortedRects:f.reduce((e,t,n)=>{let r=s.get(t);return r&&(e[n]=r),e},Array(f.length)),strategy:o}),[v,u,N.draggable,N.droppable,w,f,b,s,h,o]);return g().createElement(td.Provider,{value:y},t)}let th=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return to(n,r,o).indexOf(t)},tf=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!!c&&!!r&&(l===i||o!==a)&&(!!n||a!==o&&t===s)},tm={duration:200,easing:"ease"},tp="transform",tg=B.Transition.toString({property:tp,duration:0,easing:"linear"}),tv={roleDescription:"sortable"};function tb(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let tx=[l.Down,l.Right,l.Up,l.Left],tO=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:i,over:a,scrollableAncestors:s}}=t;if(tx.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];i.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=o.get(n.id);if(i)switch(e.code){case l.Down:r.top<i.top&&t.push(n);break;case l.Up:r.top>i.top&&t.push(n);break;case l.Left:r.left>i.left&&t.push(n);break;case l.Right:r.left<i.left&&t.push(n)}});let c=el({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null}),d=eo(c,"id");if(d===(null==a?void 0:a.id)&&c.length>1&&(d=c[1].id),null!=d){let e=i.get(n.id),t=i.get(d),a=t?o.get(t.id):null,l=null==t?void 0:t.node.current;if(l&&a&&e&&t){let n=em(l).some((e,t)=>s[t]!==e),o=tw(e,t),i=function(e,t){return!!tb(e)&&!!tb(t)&&!!tw(e,t)&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),c=n||!o?{x:0,y:0}:{x:i?r.width-a.width:0,y:i?r.height-a.height:0},d={x:a.left,y:a.top};return c.x&&c.y?d:I(d,c)}}}};function tw(e,t){return!!tb(e)&&!!tb(t)&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}var tN=n(88808),ty=n(51976),tD=n(29492);let tj=(0,tD.A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),tE=(0,tD.A)("Telescope",[["path",{d:"m10.065 12.493-6.18 1.318a.934.934 0 0 1-1.108-.702l-.537-2.15a1.07 1.07 0 0 1 .691-1.265l13.504-4.44",key:"k4qptu"}],["path",{d:"m13.56 11.747 4.332-.924",key:"19l80z"}],["path",{d:"m16 21-3.105-6.21",key:"7oh9d"}],["path",{d:"M16.485 5.94a2 2 0 0 1 1.455-2.425l1.09-.272a1 1 0 0 1 1.212.727l1.515 6.06a1 1 0 0 1-.727 1.213l-1.09.272a2 2 0 0 1-2.425-1.455z",key:"m7xp4m"}],["path",{d:"m6.158 8.633 1.114 4.456",key:"74o979"}],["path",{d:"m8 21 3.105-6.21",key:"1fvxut"}],["circle",{cx:"12",cy:"13",r:"2",key:"1c1ljs"}]]);var tC=n(54299),t_=n(48382);let tU=(0,tD.A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var tM=n(61825),tT=n(47580);let tL=(0,tD.A)("ListChecks",[["path",{d:"m3 17 2 2 4-4",key:"1jhpwq"}],["path",{d:"m3 7 2 2 4-4",key:"1obspn"}],["path",{d:"M13 6h8",key:"15sg57"}],["path",{d:"M13 12h8",key:"h98zly"}],["path",{d:"M13 18h8",key:"oe0vm4"}]]),tk=(0,tD.A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var tF=n(43596),tS=n(33284),tA=n(20625),tP=n(15992),tR=n(14735);let tI=(0,tD.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),tz=(0,tD.A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),tq=(0,tD.A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),tB=(0,tD.A)("Undo2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11",key:"f3b9sd"}]]);var tV=n(39103),tH=n(24870),tY=n(78656),tG=n(72372);let tX=(0,tD.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);var tK=n(87571),tW=n(62019),t$=n(19936),tJ=n(3619),tZ=n(59708),tQ=n(51731);function t0(e){let t=(0,tQ.M)(()=>(0,tJ.OQ)(e)),{isStatic:n}=(0,p.useContext)(tZ.Q);if(n){let[,n]=(0,p.useState)(e);(0,p.useEffect)(()=>t.on("change",n),[])}return t}var t1=n(21142);let t2=e=>e&&"object"==typeof e&&e.mix,t4=e=>t2(e)?e.mix:void 0;var t5=n(3618),t3=n(98418);function t7(e,t){let n=t0(t()),r=()=>n.set(t());return r(),(0,t5.E)(()=>{let t=()=>t3.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,t3.WG)(r)}}),n}function t8(e,t,n,r){if("function"==typeof e){tJ.bt.current=[],e();let t=t7(tJ.bt.current,e);return tJ.bt.current=void 0,t}let o="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,r=e[0+n],o=e[1+n],i=e[2+n],a=e[3+n],l=(0,t1.G)(o,i,{mixer:t4(i[0]),...a});return t?l(r):l}(t,n,r);return Array.isArray(e)?t9(e,o):t9([e],([e])=>o(e))}function t9(e,t){let n=(0,tQ.M)(()=>[]);return t7(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}var t6=n(24824);!function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/checkbox'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toggle-group'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/missions-tab'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}();let ne=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).flatMap(e=>e.themes),nt={comprendre:{icon:tN.A,label:"Comprendre"},observer:{icon:ty.A,label:"Observer"},proteger:{icon:tj,label:"Prot\xe9ger"}},nn={comprendre:{badge:"bg-cop-comprendre text-background hover:bg-cop-comprendre",filterBadge:"border-cop-comprendre text-cop-comprendre",border:"border-cop-comprendre",bg:"bg-cop-comprendre",text:"text-cop-comprendre",icon:tN.A},observer:{badge:"bg-cop-observer text-background hover:bg-cop-observer",filterBadge:"border-cop-observer text-cop-observer",border:"border-cop-observer",bg:"bg-cop-observer",text:"text-cop-observer",icon:tE},proteger:{badge:"bg-cop-proteger text-background hover:bg-cop-proteger",filterBadge:"border-cop-proteger text-cop-proteger",border:"border-cop-proteger",bg:"bg-cop-proteger",text:"text-cop-proteger",icon:tC.A}};function nr(){(0,v.useRouter)();let e=(0,v.useParams)(),t=e.stageId?parseInt(e.stageId,10):null,{toast:n}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[r,o]=(0,p.useState)(null),[i,a]=(0,p.useState)([]),[l,s]=(0,p.useState)([]),[c,d]=(0,p.useState)(null),[u,h]=(0,p.useState)(!0),[f,g]=(0,p.useState)(null),[b,O]=(0,p.useState)("objectives"),[w,N]=(0,p.useState)([]),[y,D]=(0,p.useState)(new Set),j=(0,p.useMemo)(()=>{let e=i.find(e=>e.selected_content?.program?.length)||i[0];if(e&&e.selected_content?.themes){let t=e.selected_content.themes;return ne.filter(e=>t.includes(e.title)).map(e=>e.id)}return[]},[i]),E=(0,p.useCallback)(async()=>{if(!t){g("ID de stage manquant."),h(!1);return}h(!0),g(null);try{let[e,n,r,i]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(t),Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(t),Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(t),Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())()]);if(!e||!i){g("Impossible de charger les donn\xe9es critiques."),h(!1);return}o(e),a(n.sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime())),s(r),d(i);let l=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.map(e=>({id:`q${e.id}`,question:e.question,answer:e.objectif,option_id:e.dimension.toLowerCase(),priority:"essential",status:"validated",type:"Question",duration:0,tags_theme:e.tags_theme||[],tags_filtre:e.tags_filtre||[],niveau:e.niveau,tags:[],tip:e.tip})),c=n.find(e=>e.selected_content?.program?.length)||n[0];if(c&&c.selected_content?.program){let e=new Set(c.selected_content.program),t=l.filter(t=>e.has(t.id));N(t)}else N([]);let u=localStorage.getItem(`completed_objectives_${t}`);u&&D(new Set(JSON.parse(u)))}catch(e){console.error(e),g("Une erreur est survenue lors du chargement des donn\xe9es.")}finally{h(!1)}},[t]);if(u)return(0,m.jsxs)("div",{className:"flex justify-center items-center min-h-[calc(100vh-200px)]",children:[(0,m.jsx)(t_.A,{className:"w-8 h-8 animate-spin text-primary"}),(0,m.jsx)("p",{className:"ml-4 text-muted-foreground",children:"Chargement du stage..."})]});if(f||!r||!c)return(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"m-auto mt-10 max-w-lg text-center border-destructive",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center justify-center gap-2 text-destructive",children:[(0,m.jsx)(tU,{})," Erreur"]})}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsx)("p",{className:"text-muted-foreground",children:f||"Donn\xe9es du stage introuvables."})}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",asChild:!0,className:"w-full",children:(0,m.jsxs)(x(),{href:"/stages",children:[(0,m.jsx)(tM.A,{className:"mr-2 h-4 w-4"})," Retour"]})})})]});let[C,..._]=r.title.split(" - "),U=_.join(" - ");return(0,m.jsxs)("div",{className:"space-y-6",children:[(0,m.jsxs)(x(),{href:"/stages",className:"text-sm text-muted-foreground hover:text-foreground flex items-center gap-1",children:[(0,m.jsx)(tM.A,{className:"w-4 h-4"}),"Retour \xe0 tous les stages"]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden relative bg-stage-header bg-cover bg-center",children:[(0,m.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/80 via-black/50 to-black/0"}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"relative text-white p-6 space-y-6",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-0",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-3xl font-bold font-headline",children:C}),U&&(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg text-white/90",children:U}),(0,m.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-x-4 gap-y-1 pt-2 text-sm text-white/80",children:[(0,m.jsxs)("span",{className:"flex items-center gap-1.5",children:[(0,m.jsx)(tT.A,{className:"w-4 h-4"})," ",(0,tK.GP)((0,tW.H)(r.start_date),"d MMM",{locale:t$.fr})," - ",(0,tK.GP)((0,tW.H)(r.end_date),"d MMM yyyy",{locale:t$.fr})]}),(0,m.jsxs)("span",{className:"flex items-center gap-1.5",children:[(0,m.jsx)(ty.A,{className:"w-4 h-4"})," ",r.participants," participants"]})]})]}),(0,m.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"objectives"===b?"secondary":"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("objectives"!==b&&"bg-white/10 border-white/20 text-white hover:bg-white/20"),onClick:()=>O("objectives"),children:[(0,m.jsx)(tL,{className:"mr-2 h-4 w-4"}),"Objectifs"]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"programme"===b?"secondary":"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("programme"!==b&&"bg-white/10 border-white/20 text-white hover:bg-white/20"),onClick:()=>O("programme"),children:[(0,m.jsx)(tk,{className:"mr-2 h-4 w-4"}),"Programme"]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"missions"===b?"secondary":"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("missions"!==b&&"bg-white/10 border-white/20 text-white hover:bg-white/20"),onClick:()=>O("missions"),children:[(0,m.jsx)(tF.A,{className:"mr-2 h-4 w-4"}),"Missions"]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"games"===b?"secondary":"outline",className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("games"!==b&&"bg-white/10 border-white/20 text-white hover:bg-white/20"),onClick:()=>O("games"),children:[(0,m.jsx)(tS.A,{className:"mr-2 h-4 w-4"}),"Jeux"]})]})]})]}),(0,m.jsxs)("div",{className:"mt-6",children:["objectives"===b&&(0,m.jsx)(no,{objectives:w,completedObjectives:y,onToggleObjective:e=>{let n=new Set(y);n.has(e)?n.delete(e):n.add(e),D(n),t&&localStorage.setItem(`completed_objectives_${t}`,JSON.stringify(Array.from(n)))}}),"programme"===b&&(0,m.jsx)(ni,{stage:r,sorties:i,etagesData:c,onSave:e=>{N(e),E()}}),"missions"===b&&t&&(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/missions-tab'");throw e.code="MODULE_NOT_FOUND",e}()),{stageId:t,stageType:r.type,stageThemes:j}),"games"===b&&(0,m.jsx)(ns,{games:l,stageId:r.id})]})]})}let no=({objectives:e,completedObjectives:t,onToggleObjective:n})=>{let[r,o]=(0,p.useState)([]),[i,a]=(0,p.useState)(!1),l=(0,p.useMemo)(()=>{let t=new Set;return e.forEach(e=>{e.tags_theme.forEach(e=>t.add(e))}),ne.filter(e=>t.has(e.id))},[e]),s=(0,p.useMemo)(()=>{let n={comprendre:[],observer:[],proteger:[]},o=e;return i&&(o=o.filter(e=>!t.has(e.id))),r.length>0&&(o=o.filter(e=>r.some(t=>e.tags_theme.includes(t)))),o.forEach(e=>{let t=e.option_id;n[t]&&!n[t].some(t=>t.id===e.id)&&n[t].push(e)}),n},[e,r,i,t]),c=e=>{o(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])};return 0===e.length?(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center py-16 px-4",children:[(0,m.jsx)("h3",{className:"text-lg font-semibold",children:"Le programme est vide"}),(0,m.jsx)("p",{className:"text-muted-foreground mt-1 mb-4",children:'Allez dans l\'onglet "Programme" pour d\xe9finir les objectifs de ce stage.'})]})}):(0,m.jsxs)("div",{className:"space-y-8",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-4 flex flex-col sm:flex-row gap-4",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-full sm:w-auto",children:[(0,m.jsx)(tA.A,{className:"mr-2 h-4 w-4"}),"Th\xe8mes ",r.length>0&&`(${r.length})`]})}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Filtrer par th\xe8me"}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{}),l.map(e=>(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/dropdown-menu'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:r.includes(e.id),onCheckedChange:()=>c(e.id),children:e.title},e.id))]})]}),(0,m.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/checkbox'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"show-not-seen",checked:i,onCheckedChange:e=>a(!!e)}),(0,m.jsx)("label",{htmlFor:"show-not-seen",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Afficher uniquement les non vus"})]})]})}),Object.entries(s).map(([e,r])=>{let{icon:o,label:i}=nt[e]||{icon:"div",label:e},a=nn[e]||{};return 0===r.length?null:(0,m.jsxs)("div",{children:[(0,m.jsxs)("h3",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-xl font-semibold flex items-center gap-3 mb-4",a.text),children:[(0,m.jsx)(o,{className:"w-6 h-6"}),i]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"multiple",className:"w-full space-y-2",children:r.map(e=>(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.id,className:"border-b-0",children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("transition-all overflow-hidden",t.has(e.id)?"bg-card opacity-50":"bg-card"),children:[(0,m.jsxs)("div",{className:"flex items-center p-1 justify-between",children:[(0,m.jsxs)("div",{className:"flex items-center flex-grow",children:[(0,m.jsx)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-1.5 h-auto self-stretch rounded-full shrink-0",a.bg)}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow p-3 text-left hover:no-underline",children:(0,m.jsxs)("div",{className:"flex flex-col items-start gap-2 text-left",children:[(0,m.jsx)("div",{className:"flex flex-wrap gap-1",children:e.tags_theme.map(e=>{let t=ne.find(t=>t.id===e);return t?(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"font-normal",children:t.title},e):null})}),(0,m.jsx)("p",{className:"font-medium text-foreground text-sm text-left",children:e.question})]})})]}),(0,m.jsxs)("div",{className:"flex items-center gap-2 pl-2 pr-1 shrink-0",children:[(0,m.jsx)(nc,{isCompleted:t.has(e.id),onToggle:()=>n(e.id)}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-1 hover:no-underline [&>svg]:mx-auto",children:(0,m.jsx)(tP.A,{className:"w-5 h-5 text-muted-foreground transition-transform duration-200"})})]})]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/accordion'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)("div",{className:"border-t mx-3"}),(0,m.jsxs)("div",{className:"px-3 pb-3 pt-2 text-muted-foreground text-sm space-y-2",children:[(0,m.jsxs)("p",{children:[(0,m.jsx)("span",{className:"font-semibold text-foreground/80",children:"Objectif:"})," ",e.answer]}),e.tip&&(0,m.jsxs)("p",{children:[(0,m.jsx)("span",{className:"font-semibold text-foreground/80",children:"Conseil:"})," ",e.tip]})]})]})]})},e.id))})]},e)})]})},ni=({stage:e,sorties:t,etagesData:n,onSave:r})=>{let{toast:o}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[i,a]=(0,p.useTransition)(),l=(0,p.useCallback)(()=>{let e=Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).questions_copun.map(e=>({id:`q${e.id}`,question:e.question,answer:e.objectif,option_id:e.dimension.toLowerCase(),priority:"essential",status:"validated",type:"Question",duration:0,tags_theme:e.tags_theme||[],tags_filtre:e.tags_filtre||[],niveau:e.niveau,tags:[],tip:e.tip})),n=t.find(e=>e.selected_content?.program?.length)||t[0],r=new Set,o=0,i=[];if(n){let e=n.selected_content||{},t=n.selected_notions||{};e.program&&(r=new Set(e.program)),t.niveau&&(o=t.niveau),e.themes&&(i=e.themes.map(e=>ne.find(t=>t.title===e)?.id).filter(e=>!!e))}let a=e.filter(e=>r.has(e.id));return{items:{available:e.filter(e=>!r.has(e.id)),selected:a},level:o,themeIds:i}},[t]),[s,c]=(0,p.useState)(l().level),[d,u]=(0,p.useState)(l().themeIds),[h,f]=(0,p.useState)(l().items),[g,v]=(0,p.useState)("explore"),[b,O]=(0,p.useState)(null);(0,p.useEffect)(()=>{let e=l();c(e.level),u(e.themeIds),f(e.items)},[t,l]);let w=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(Z(eP),Z(eL,{coordinateGetter:tO})),N=e=>{f(t=>({available:t.available.filter(t=>t.id!==e.id),selected:[...t.selected,e]}))},y=e=>{f(t=>({selected:t.selected.filter(t=>t.id!==e.id),available:[...t.available,e].sort((e,t)=>e.question.localeCompare(t.question))}))},D=(0,p.useMemo)(()=>{let e=h.available.filter(e=>e.niveau===s+1);return d.length>0&&(e=e.filter(e=>d.some(t=>e.tags_theme.includes(t)))),e},[h.available,s,d]);return(0,m.jsxs)("div",{className:"space-y-6",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"\xc9tape 1 : \xc0 qui je parle ? (Niveau du groupe)"})}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)("div",{className:"sm:hidden",children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:s.toString(),onValueChange:e=>e&&c(parseInt(e)),children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"S\xe9lectionnez un niveau"})}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.niveau.options.map((e,t)=>(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:t.toString(),children:e.label},e.id))})]})}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toggle-group'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"single",value:s.toString(),onValueChange:e=>e&&c(parseInt(e)),className:"hidden sm:grid sm:grid-cols-3 gap-2",children:n.niveau.options.map((e,t)=>(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/toggle-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:t.toString(),className:"h-auto flex-col items-start p-3 data-[state=on]:bg-primary/10 data-[state=on]:text-primary data-[state=on]:border-primary",children:[(0,m.jsx)("p",{className:"font-semibold",children:e.label}),(0,m.jsx)("p",{className:"text-xs text-left font-normal text-muted-foreground",children:e.tip})]},e.id))})]})]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"\xc9tape 2 : De quoi je parle ?"}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"S\xe9lectionnez un ou plusieurs th\xe8mes qui serviront de fil conducteur."})]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:Object(function(){var e=Error("Cannot find module '@/data/etages'");throw e.code="MODULE_NOT_FOUND",e}()).map(e=>(0,m.jsxs)("div",{children:[(0,m.jsx)("h4",{className:"font-semibold text-muted-foreground mb-2",children:e.label}),(0,m.jsx)("div",{className:"flex flex-wrap gap-1.5",children:e.themes.map(e=>(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>u(t=>t.includes(e.id)?t.filter(t=>t!==e.id):[...t,e.id]),variant:d.includes(e.id)?"default":"secondary",className:"cursor-pointer py-1",children:[(0,m.jsx)(e.icon,{className:"w-3.5 h-3.5 mr-1.5"}),e.title]},e.id))})]},e.label))})]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"\xc9tape 3 : Pourquoi j’en parle ? (Construire le programme)"}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"S\xe9lectionnez les fiches-objectifs pour construire vos s\xe9ances."})]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:g,onValueChange:v,className:"w-full",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full grid-cols-2",children:[(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"explore",children:["Explorer les fiches (",D.length,")"]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"selected",children:["Mon Programme (",h.selected.length,")"]})]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"explore",className:"mt-4",children:[(0,m.jsxs)("div",{className:"flex gap-2 mb-4",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toggle-group'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"single",value:b??"",onValueChange:e=>O(e||null),children:Object.entries(nn).map(([e,{filterBadge:t,icon:n,text:r}])=>(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/toggle-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("border",b===e&&t),children:[(0,m.jsx)(n,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("w-4 h-4 mr-2",b===e?r:"text-muted-foreground")}),e.charAt(0).toUpperCase()+e.slice(1)]},e))}),b&&(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:()=>O(null),children:"Effacer"})]}),(0,m.jsx)("div",{className:"min-h-[400px] max-h-[60vh] overflow-y-auto bg-muted/50 p-2 rounded-lg space-y-2",children:D.length>0?D.map(e=>(0,m.jsx)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("transition-opacity",b&&b!==e.option_id&&"opacity-40 hover:opacity-100"),children:(0,m.jsx)(na,{card:e,onAdd:()=>N(e)})},e.id)):(0,m.jsx)("p",{className:"p-4 text-center text-sm text-muted-foreground",children:"Aucune fiche \xe0 afficher. Essayez de changer les filtres ou le niveau du stage."})})]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"selected",className:"mt-4",children:(0,m.jsx)(te,{sensors:w,collisionDetection:ea,onDragEnd:e=>{let{active:t,over:n}=e;n&&t.id!==n.id&&f(e=>{let r=e.selected.findIndex(e=>e.id===t.id),o=e.selected.findIndex(e=>e.id===n.id);return{...e,selected:to(e.selected,r,o)}})},children:(0,m.jsx)("div",{className:"min-h-[400px] max-h-[60vh] overflow-y-auto bg-muted/50 p-2 rounded-lg space-y-2",children:(0,m.jsx)(tu,{items:h.selected,strategy:ts,children:h.selected.length>0?h.selected.map(e=>(0,m.jsx)(nl,{card:e,onRemove:()=>y(e)},e.id)):(0,m.jsx)("p",{className:"p-4 text-center text-sm text-muted-foreground",children:'Cliquez sur les fiches dans l\'onglet "Explorer" pour les ajouter ici.'})})})})})]})})]}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"\xc9tape 4 : Action de l’encadrant (Finalisation)"})}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col sm:flex-row gap-4",children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,className:"w-full",disabled:0===h.selected.length,children:(0,m.jsxs)(x(),{href:`/jeux/validation?stageId=${e.id}&objectives=${h.selected.map(e=>e.id.replace("q","")).join(",")}`,children:[(0,m.jsx)(tR.A,{className:"mr-2 h-4 w-4"}),"Je teste mes connaissances"]})}),(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",onClick:()=>{a(async()=>{if(!e)return;let t=d.map(e=>ne.find(t=>t.id===e)?.title).filter(e=>!!e),n=await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e.id,s,t,h.selected.map(e=>e.id));n.success?(o({title:"Programme sauvegard\xe9",description:"Le programme a \xe9t\xe9 mis \xe0 jour avec succ\xe8s."}),r(h.selected)):o({title:"Erreur",description:n.error||"La sauvegarde a \xe9chou\xe9.",variant:"destructive"})})},disabled:i,children:[i?(0,m.jsx)(t_.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,m.jsx)(tI,{className:"mr-2 h-4 w-4"}),"Appliquer le programme"]})]})]})]})},na=({card:e,onAdd:t})=>{let n=e.option_id,r=nn[n]||{};return(0,m.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-2 bg-card border-l-4 flex items-center gap-2 rounded-md shadow-sm",r.border),children:[(0,m.jsxs)("div",{className:"flex-grow",children:[(0,m.jsx)("p",{className:"text-sm font-semibold",children:e.question}),(0,m.jsx)("p",{className:"text-xs text-muted-foreground",children:e.answer})]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(r.badge,"pointer-events-none"),children:n}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"icon",variant:"ghost",onClick:t,className:"h-8 w-8 shrink-0",children:(0,m.jsx)(tz,{className:"h-4 w-4"})})]})},nl=({card:e,onRemove:t})=>{let{attributes:n,listeners:r,setNodeRef:i,transform:a,transition:l,isDragging:s}=function(e){var t,n,r,i;let{animateLayoutChanges:a=tf,attributes:l,disabled:s,data:c,getNewIndex:d=th,id:u,strategy:h,resizeObserverConfig:f,transition:m=tm}=e,{items:g,containerId:v,activeIndex:b,disabled:x,disableTransforms:O,sortedRects:w,overIndex:N,useDragOverlay:y,strategy:D}=(0,p.useContext)(td),j=(t=s,n=x,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(r=null==t?void 0:t.draggable)?r:n.draggable,droppable:null!=(i=null==t?void 0:t.droppable)?i:n.droppable}),E=g.indexOf(u),C=(0,p.useMemo)(()=>({sortable:{containerId:v,index:E,items:g},...c}),[v,c,E,g]),_=(0,p.useMemo)(()=>g.slice(g.indexOf(u)),[g,u]),{rect:M,node:L,isOver:F,setNodeRef:S}=function(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=A("Droppable"),{active:l,dispatch:s,over:c,measureDroppableContainers:d}=(0,p.useContext)(e5),u=(0,p.useRef)({disabled:n}),h=(0,p.useRef)(!1),f=(0,p.useRef)(null),m=(0,p.useRef)(null),{disabled:g,updateMeasurementsFor:v,timeout:b}={...tr,...i},x=T(null!=v?v:r),O=eY({callback:(0,p.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=m.current&&clearTimeout(m.current),m.current=setTimeout(()=>{d(Array.isArray(x.current)?x.current:[x.current]),m.current=null},b)},[b]),disabled:g||!l}),[w,N]=k((0,p.useCallback)((e,t)=>{O&&(t&&(O.unobserve(t),h.current=!1),e&&O.observe(e))},[O])),y=T(t);return(0,p.useEffect)(()=>{O&&w.current&&(O.disconnect(),h.current=!1,O.observe(w.current))},[w,O]),(0,p.useEffect)(()=>(s({type:o.RegisterDroppable,element:{id:r,key:a,disabled:n,node:w,rect:f,data:y}}),()=>s({type:o.UnregisterDroppable,key:a,id:r})),[r]),(0,p.useEffect)(()=>{n!==u.current.disabled&&(s({type:o.SetDroppableDisabled,id:r,key:a,disabled:n}),u.current.disabled=n)},[r,a,n,s]),{active:l,rect:f,isOver:(null==c?void 0:c.id)===r,node:w,over:c,setNodeRef:N}}({id:u,data:C,disabled:j.droppable,resizeObserverConfig:{updateMeasurementsFor:_,...f}}),{active:P,activatorEvent:R,activeNodeRect:I,attributes:q,setNodeRef:V,listeners:H,isDragging:Y,over:G,setActivatorNodeRef:X,transform:K}=function(e){let{id:t,data:n,disabled:r=!1,attributes:o}=e,i=A("Draggable"),{activators:a,activatorEvent:l,active:s,activeNodeRect:c,ariaDescribedById:d,draggableNodes:u,over:h}=(0,p.useContext)(e5),{role:f=tn,roleDescription:m="draggable",tabIndex:g=0}=null!=o?o:{},v=(null==s?void 0:s.id)===t,b=(0,p.useContext)(v?e6:tt),[x,O]=k(),[w,N]=k(),y=(0,p.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:o}=n;return e[r]=e=>{o(e,t)},e},{}),[a,t]),D=T(n);return U(()=>(u.set(t,{id:t,key:i,node:x,activatorNode:w,data:D}),()=>{let e=u.get(t);e&&e.key===i&&u.delete(t)}),[u,t]),{active:s,activatorEvent:l,activeNodeRect:c,attributes:(0,p.useMemo)(()=>({role:f,tabIndex:g,"aria-disabled":r,"aria-pressed":!!v&&f===tn||void 0,"aria-roledescription":m,"aria-describedby":d.draggable}),[r,f,g,v,m,d.draggable]),isDragging:v,listeners:r?void 0:y,node:x,over:h,setNodeRef:O,setActivatorNodeRef:N,transform:b}}({id:u,data:C,attributes:{...tv,...l},disabled:j.draggable}),W=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(S,V),$=!!P,J=$&&!O&&ti(b)&&ti(N),Z=!y&&Y,Q=Z&&J?K:null,ee=J?null!=Q?Q:(null!=h?h:D)({rects:w,activeNodeRect:I,activeIndex:b,overIndex:N,index:E}):null,et=ti(b)&&ti(N)?d({id:u,items:g,activeIndex:b,overIndex:N}):E,en=null==P?void 0:P.id,er=(0,p.useRef)({activeId:en,items:g,newIndex:et,containerId:v}),eo=g!==er.current.items,ei=a({active:P,containerId:v,isDragging:Y,isSorting:$,id:u,index:E,items:g,newIndex:er.current.newIndex,previousItems:er.current.items,previousContainerId:er.current.containerId,transition:m,wasDragging:null!=er.current.activeId}),ea=function(e){let{disabled:t,index:n,node:r,rect:o}=e,[i,a]=(0,p.useState)(null),l=(0,p.useRef)(n);return U(()=>{if(!t&&n!==l.current&&r.current){let e=o.current;if(e){let t=eh(r.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&a(n)}}n!==l.current&&(l.current=n)},[t,n,r,o]),(0,p.useEffect)(()=>{i&&a(null)},[i]),i}({disabled:!ei,index:E,node:L,rect:M});return(0,p.useEffect)(()=>{$&&er.current.newIndex!==et&&(er.current.newIndex=et),v!==er.current.containerId&&(er.current.containerId=v),g!==er.current.items&&(er.current.items=g)},[$,et,v,g]),(0,p.useEffect)(()=>{if(en===er.current.activeId)return;if(en&&!er.current.activeId){er.current.activeId=en;return}let e=setTimeout(()=>{er.current.activeId=en},50);return()=>clearTimeout(e)},[en]),{active:P,activeIndex:b,attributes:q,data:C,rect:M,index:E,newIndex:et,items:g,isOver:F,isSorting:$,isDragging:Y,listeners:H,node:L,overIndex:N,over:G,setNodeRef:W,setActivatorNodeRef:X,setDroppableNodeRef:S,setDraggableNodeRef:V,transform:null!=ea?ea:ee,transition:ea||eo&&er.current.newIndex===E?tg:(!Z||z(R))&&m&&($||ei)?B.Transition.toString({...m,property:tp}):void 0}}({id:e.id}),c={transform:B.Transform.toString(a),transition:l,opacity:s?.5:1,zIndex:s?10:"auto"},d=e.option_id,u=nn[d]||{};return(0,m.jsxs)("div",{ref:i,style:c,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-2 bg-card border-l-4 flex items-center gap-2 rounded-md shadow-sm",u.border),children:[(0,m.jsx)("span",{...r,...n,className:"cursor-grab active:cursor-grabbing p-1",children:(0,m.jsx)(tq,{className:"h-5 w-5 text-muted-foreground"})}),(0,m.jsxs)("div",{className:"flex-grow",children:[(0,m.jsx)("p",{className:"text-sm font-semibold",children:e.question}),(0,m.jsx)("p",{className:"text-xs text-muted-foreground",children:e.answer})]}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(u.badge,"pointer-events-none"),children:d}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"icon",variant:"ghost",onClick:t,className:"h-8 w-8 shrink-0 text-muted-foreground hover:text-foreground",children:(0,m.jsx)(tB,{className:"h-4 w-4"})})]})},ns=({games:e,stageId:t})=>{let n=`/jeux/generateur?stageId=${t}`;return(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,m.jsxs)("div",{className:"flex justify-between items-center",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Jeux de R\xe9vision"}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cr\xe9ez et lancez des jeux pour r\xe9viser les notions."})]}),(0,m.jsx)(x(),{href:n,children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,m.jsx)(tV.A,{className:"mr-2 h-4 w-4"}),"Cr\xe9er un jeu"]})})]})}),(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e.length>0?(0,m.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,m.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:bg-muted transition-colors group relative",children:(0,m.jsx)(x(),{href:`/jeux/${e.id}`,children:(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"p-4 flex justify-between items-center",children:[(0,m.jsxs)("div",{children:[(0,m.jsx)("p",{className:"font-semibold",children:e.title}),(0,m.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Th\xe8me: ",e.theme," - Cr\xe9\xe9 le ",(0,tK.GP)(new Date(e.created_at),"d MMM yyyy",{locale:t$.fr})]})]}),(0,m.jsxs)("div",{className:"flex items-center gap-2",children:[(0,m.jsx)(tS.A,{className:"w-5 h-5 text-muted-foreground"}),(0,m.jsx)(tH.A,{className:"w-4 h-4 text-muted-foreground"})]})]})})},e.id))}):(0,m.jsxs)("div",{className:"text-center py-10 px-4 border-2 border-dashed rounded-lg",children:[(0,m.jsx)("h3",{className:"text-lg font-semibold",children:"Aucun jeu cr\xe9\xe9"}),(0,m.jsx)("p",{className:"text-muted-foreground mt-1",children:"Cr\xe9ez votre premier jeu pour ce stage."})]})})]})},nc=({isCompleted:e,onToggle:t})=>{let n=t0(0);(0,p.useEffect)(()=>{n.set(0)},[e,n]);let r=t8(n,[0,36],[1,0]),o=t8(n,[84,120],[0,1]);return e?(0,m.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:t,className:"text-xs h-8 px-2 text-green-500 hover:text-green-400",children:[(0,m.jsx)(tY.A,{className:"w-4 h-4 mr-1"}),"Notion vue"]}):(0,m.jsxs)("div",{className:"relative w-40 h-8 bg-secondary rounded-full flex items-center justify-center p-1 overflow-hidden",children:[(0,m.jsx)(t6.P.span,{className:"text-xs font-medium text-muted-foreground z-0 select-none",style:{opacity:r},children:"Glisser pour valider"}),(0,m.jsxs)(t6.P.div,{drag:"x",dragConstraints:{left:0,right:120},style:{x:n},onDragEnd:(e,r)=>{r.offset.x>84&&t(),n.set(0)},className:"absolute top-1 left-1 h-6 w-6 rounded-full bg-background shadow flex items-center justify-center z-10 cursor-grab active:cursor-grabbing",dragMomentum:!1,dragElastic:.1,children:[(0,m.jsx)(t6.P.div,{style:{opacity:o},className:"text-green-500",children:(0,m.jsx)(tG.A,{className:"w-4 h-4"})}),(0,m.jsx)(t6.P.div,{style:{opacity:r},className:"absolute text-muted-foreground",children:(0,m.jsx)(tX,{className:"w-4 h-4"})})]})]})}},47580:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51976:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},53656:(e,t,n)=>{Promise.resolve().then(n.bind(n,58835))},54299:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},58835:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\stages\\\\[stageId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\stages\\[stageId]\\page.tsx","default")},61825:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},62019:(e,t,n)=>{"use strict";n.d(t,{H:()=>o});var r=n(29490);function o(e,t){let n,o,f=t?.additionalDigits??2,m=function(e){let t,n={},r=e.split(i.dateTimeDelimiter);if(r.length>2)return n;if(/:/.test(r[0])?t=r[0]:(n.date=r[0],t=r[1],i.timeZoneDelimiter.test(n.date)&&(n.date=e.split(i.timeZoneDelimiter)[0],t=e.substr(n.date.length,e.length))),t){let e=i.timezone.exec(t);e?(n.time=t.replace(e[1],""),n.timezone=e[1]):n.time=t}return n}(e);if(m.date){let e=function(e,t){let n=RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+t)+"})|(\\d{2}|[+-]\\d{"+(2+t)+"})$)"),r=e.match(n);if(!r)return{year:NaN,restDateString:""};let o=r[1]?parseInt(r[1]):null,i=r[2]?parseInt(r[2]):null;return{year:null===i?o:100*i,restDateString:e.slice((r[1]||r[2]).length)}}(m.date,f);n=function(e,t){var n,r,o,i,l,s,d,f;if(null===t)return new Date(NaN);let m=e.match(a);if(!m)return new Date(NaN);let p=!!m[4],g=c(m[1]),v=c(m[2])-1,b=c(m[3]),x=c(m[4]),O=c(m[5])-1;if(p){return(n=0,r=x,o=O,r>=1&&r<=53&&o>=0&&o<=6)?function(e,t,n){let r=new Date(0);r.setUTCFullYear(e,0,4);let o=r.getUTCDay()||7;return r.setUTCDate(r.getUTCDate()+((t-1)*7+n+1-o)),r}(t,x,O):new Date(NaN)}{let e=new Date(0);return(i=t,l=v,s=b,l>=0&&l<=11&&s>=1&&s<=(u[l]||(h(i)?29:28))&&(d=t,(f=g)>=1&&f<=(h(d)?366:365)))?(e.setUTCFullYear(t,v,Math.max(g,b)),e):new Date(NaN)}}(e.restDateString,e.year)}if(!n||isNaN(n.getTime()))return new Date(NaN);let p=n.getTime(),g=0;if(m.time&&isNaN(g=function(e){var t,n,o;let i=e.match(l);if(!i)return NaN;let a=d(i[1]),s=d(i[2]),c=d(i[3]);return(t=a,n=s,o=c,24===t?0===n&&0===o:o>=0&&o<60&&n>=0&&n<60&&t>=0&&t<25)?a*r.s0+s*r.Cg+1e3*c:NaN}(m.time)))return new Date(NaN);if(m.timezone){if(isNaN(o=function(e){var t,n;if("Z"===e)return 0;let o=e.match(s);if(!o)return 0;let i="+"===o[1]?-1:1,a=parseInt(o[2]),l=o[3]&&parseInt(o[3])||0;return(t=0,(n=l)>=0&&n<=59)?i*(a*r.s0+l*r.Cg):NaN}(m.timezone)))return new Date(NaN)}else{let e=new Date(p+g),t=new Date(0);return t.setFullYear(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()),t.setHours(e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()),t}return new Date(p+g+o)}let i={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},a=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,l=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,s=/^([+-])(\d{2})(?::?(\d{2}))?$/;function c(e){return e?parseInt(e):1}function d(e){return e&&parseFloat(e.replace(",","."))||0}let u=[31,null,31,30,31,30,31,31,30,31,30,31];function h(e){return e%400==0||e%4==0&&e%100!=0}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63384:(e,t,n)=>{Promise.resolve().then(n.bind(n,47273))},72372:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},77190:()=>{},78656:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},88808:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(29492).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},94479:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,49782,23)),Promise.resolve().then(n.t.bind(n,23552,23)),Promise.resolve().then(n.t.bind(n,30708,23)),Promise.resolve().then(n.t.bind(n,17319,23)),Promise.resolve().then(n.t.bind(n,92079,23)),Promise.resolve().then(n.t.bind(n,8487,23)),Promise.resolve().then(n.t.bind(n,55543,23)),Promise.resolve().then(n.t.bind(n,42241,23))},97898:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i,metadata:()=>o});var r=n(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),n(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function i({children:e}){return(0,r.jsxs)("html",{lang:"fr",children:[(0,r.jsxs)("head",{children:[(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,r.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,r.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,r.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,r.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,r.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,r.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,r.jsxs)("body",{className:"font-body antialiased",children:[(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,r.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[683,425,808,97,410,824],()=>n(11654));module.exports=r})();