"use strict";exports.id=410,exports.ids=[410],exports.modules={9104:(e,t,n)=>{n.d(t,{o:()=>a});function a(e){return(t,n)=>{let a;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,r=n?.width?String(n.width):t;a=e.formattingValues[r]||e.formattingValues[t]}else{let t=e.defaultWidth,r=n?.width?String(n.width):e.defaultWidth;a=e.values[r]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}},18886:(e,t,n)=>{n.d(t,{k:()=>a});function a(e){return (t={})=>{let n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}},19936:(e,t,n)=>{n.d(t,{fr:()=>m});let a={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}};var r=n(18886);let i={date:(0,r.k)({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:(0,r.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,r.k)({formats:{full:"{{date}} '\xe0' {{time}}",long:"{{date}} '\xe0' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"eeee 'dernier \xe0' p",yesterday:"'hier \xe0' p",today:"'aujourd’hui \xe0' p",tomorrow:"'demain \xe0' p'",nextWeek:"eeee 'prochain \xe0' p",other:"P"};var u=n(9104);let s=["MMM","MMMM"],d={preprocessor:(e,t)=>1!==e.getDate()&&t.some(e=>e.isToken&&s.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t,ordinalNumber:(e,t)=>{let n,a=Number(e),r=t?.unit;return 0===a?"0":(n=1===a?r&&["year","week","hour","minute","second"].includes(r)?"\xe8re":"er":"\xe8me",a+n)},era:(0,u.o)({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant J\xe9sus-Christ","apr\xe8s J\xe9sus-Christ"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2\xe8me trim.","3\xe8me trim.","4\xe8me trim."],wide:["1er trimestre","2\xe8me trimestre","3\xe8me trimestre","4\xe8me trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,u.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","f\xe9vr.","mars","avr.","mai","juin","juil.","ao\xfbt","sept.","oct.","nov.","d\xe9c."],wide:["janvier","f\xe9vrier","mars","avril","mai","juin","juillet","ao\xfbt","septembre","octobre","novembre","d\xe9cembre"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"apr\xe8s-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’apr\xe8s-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})};var l=n(32484);let m={code:"fr",formatDistance:(e,t,n)=>{let r,i=a[e];if(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),n?.addSuffix)if(n.comparison&&n.comparison>0)return"dans "+r;else return"il y a "+r;return r},formatLong:i,formatRelative:(e,t,n,a)=>o[e],localize:d,match:{ordinalNumber:(0,n(45262).K)({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:(0,l.A)({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:(0,l.A)({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,l.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,l.A)({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,l.A)({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}}},29490:(e,t,n)=>{n.d(t,{Cg:()=>i,my:()=>a,s0:()=>o,w4:()=>r});let a=6048e5,r=864e5,i=6e4,o=36e5},32484:(e,t,n)=>{function a(e){return(t,n={})=>{let a,r=n.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;let u=o[0],s=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(s)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(s,e=>e.test(u)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(s,e=>e.test(u));return a=e.valueCallback?e.valueCallback(d):d,{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(u.length)}}}n.d(t,{A:()=>a})},44568:(e,t,n)=>{n.d(t,{o:()=>r});var a=n(94212);function r(e){let t=(0,a.a)(e);return t.setHours(0,0,0,0),t}},45262:(e,t,n)=>{n.d(t,{K:()=>a});function a(e){return(t,n={})=>{let a=t.match(e.matchPattern);if(!a)return null;let r=a[0],i=t.match(e.parsePattern);if(!i)return null;let o=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:t.slice(r.length)}}}},54338:(e,t,n)=>{function a(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}n.d(t,{w:()=>a})},87571:(e,t,n)=>{n.d(t,{GP:()=>F});let a={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};var r=n(18886);let i={date:(0,r.k)({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:(0,r.k)({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:(0,r.k)({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};var u=n(9104);let s={ordinalNumber:(e,t)=>{let n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:(0,u.o)({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:(0,u.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,u.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:(0,u.o)({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:(0,u.o)({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};var d=n(32484);let l={code:"en-US",formatDistance:(e,t,n)=>{let r,i=a[e];if(r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),n?.addSuffix)if(n.comparison&&n.comparison>0)return"in "+r;else return r+" ago";return r},formatLong:i,formatRelative:(e,t,n,a)=>o[e],localize:s,match:{ordinalNumber:(0,n(45262).K)({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,d.A)({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:(0,d.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,d.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,d.A)({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:(0,d.A)({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},m={};var c=n(29490),h=n(44568),f=n(94212);function g(e){let t=(0,f.a)(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),e-n}var w=n(54338);function b(e,t){let n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??m.weekStartsOn??m.locale?.options?.weekStartsOn??0,a=(0,f.a)(e),r=a.getDay();return a.setDate(a.getDate()-(7*(r<n)+r-n)),a.setHours(0,0,0,0),a}function y(e){return b(e,{weekStartsOn:1})}function v(e){let t=(0,f.a)(e),n=t.getFullYear(),a=(0,w.w)(e,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);let r=y(a),i=(0,w.w)(e,0);i.setFullYear(n,0,4),i.setHours(0,0,0,0);let o=y(i);return t.getTime()>=r.getTime()?n+1:t.getTime()>=o.getTime()?n:n-1}function p(e,t){let n=(0,f.a)(e),a=n.getFullYear(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??m.firstWeekContainsDate??m.locale?.options?.firstWeekContainsDate??1,i=(0,w.w)(e,0);i.setFullYear(a+1,0,r),i.setHours(0,0,0,0);let o=b(i,t),u=(0,w.w)(e,0);u.setFullYear(a,0,r),u.setHours(0,0,0,0);let s=b(u,t);return n.getTime()>=o.getTime()?a+1:n.getTime()>=s.getTime()?a:a-1}function M(e,t){let n=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+n}let k={y(e,t){let n=e.getFullYear(),a=n>0?n:1-n;return M("yy"===t?a%100:a,t.length)},M(e,t){let n=e.getMonth();return"M"===t?String(n+1):M(n+1,2)},d:(e,t)=>M(e.getDate(),t.length),a(e,t){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>M(e.getHours()%12||12,t.length),H:(e,t)=>M(e.getHours(),t.length),m:(e,t)=>M(e.getMinutes(),t.length),s:(e,t)=>M(e.getSeconds(),t.length),S(e,t){let n=t.length;return M(Math.trunc(e.getMilliseconds()*Math.pow(10,n-3)),t.length)}},P={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},W={G:function(e,t,n){let a=+(e.getFullYear()>0);switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if("yo"===t){let t=e.getFullYear();return n.ordinalNumber(t>0?t:1-t,{unit:"year"})}return k.y(e,t)},Y:function(e,t,n,a){let r=p(e,a),i=r>0?r:1-r;return"YY"===t?M(i%100,2):"Yo"===t?n.ordinalNumber(i,{unit:"year"}):M(i,t.length)},R:function(e,t){return M(v(e),t.length)},u:function(e,t){return M(e.getFullYear(),t.length)},Q:function(e,t,n){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return M(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){let a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return M(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){let a=e.getMonth();switch(t){case"M":case"MM":return k.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){let a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return M(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){let r=function(e,t){let n=(0,f.a)(e);return Math.round((b(n,t)-function(e,t){let n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??m.firstWeekContainsDate??m.locale?.options?.firstWeekContainsDate??1,a=p(e,t),r=(0,w.w)(e,0);return r.setFullYear(a,0,n),r.setHours(0,0,0,0),b(r,t)}(n,t))/c.my)+1}(e,a);return"wo"===t?n.ordinalNumber(r,{unit:"week"}):M(r,t.length)},I:function(e,t,n){let a=function(e){let t=(0,f.a)(e);return Math.round((y(t)-function(e){let t=v(e),n=(0,w.w)(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),y(n)}(t))/c.my)+1}(e);return"Io"===t?n.ordinalNumber(a,{unit:"week"}):M(a,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):k.d(e,t)},D:function(e,t,n){let a=function(e){let t=(0,f.a)(e);return function(e,t){let n=(0,h.o)(e),a=(0,h.o)(t);return Math.round((n-g(n)-(a-g(a)))/c.w4)}(t,function(e){let t=(0,f.a)(e),n=(0,w.w)(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(t))+1}(e);return"Do"===t?n.ordinalNumber(a,{unit:"dayOfYear"}):M(a,t.length)},E:function(e,t,n){let a=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){let r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(i);case"ee":return M(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){let r=e.getDay(),i=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(i);case"cc":return M(i,t.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){let a=e.getDay(),r=0===a?7:a;switch(t){case"i":return String(r);case"ii":return M(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){let a=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},b:function(e,t,n){let a,r=e.getHours();switch(a=12===r?P.noon:0===r?P.midnight:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},B:function(e,t,n){let a,r=e.getHours();switch(a=r>=17?P.evening:r>=12?P.afternoon:r>=4?P.morning:P.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(a,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(a,{width:"narrow",context:"formatting"});default:return n.dayPeriod(a,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return k.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):k.H(e,t)},K:function(e,t,n){let a=e.getHours()%12;return"Ko"===t?n.ordinalNumber(a,{unit:"hour"}):M(a,t.length)},k:function(e,t,n){let a=e.getHours();return(0===a&&(a=24),"ko"===t)?n.ordinalNumber(a,{unit:"hour"}):M(a,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):k.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):k.s(e,t)},S:function(e,t){return k.S(e,t)},X:function(e,t,n){let a=e.getTimezoneOffset();if(0===a)return"Z";switch(t){case"X":return S(a);case"XXXX":case"XX":return j(a);default:return j(a,":")}},x:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"x":return S(a);case"xxxx":case"xx":return j(a);default:return j(a,":")}},O:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+x(a,":");default:return"GMT"+j(a,":")}},z:function(e,t,n){let a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+x(a,":");default:return"GMT"+j(a,":")}},t:function(e,t,n){return M(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return M(e.getTime(),t.length)}};function x(e,t=""){let n=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),i=a%60;return 0===i?n+String(r):n+String(r)+t+M(i,2)}function S(e,t){return e%60==0?(e>0?"-":"+")+M(Math.abs(e)/60,2):j(e,t)}function j(e,t=""){let n=Math.abs(e);return(e>0?"-":"+")+M(Math.trunc(n/60),2)+t+M(n%60,2)}let C=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},T=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},D={p:T,P:(e,t)=>{let n,a=e.match(/(P+)(p+)?/)||[],r=a[1],i=a[2];if(!i)return C(e,t);switch(r){case"P":n=t.dateTime({width:"short"});break;case"PP":n=t.dateTime({width:"medium"});break;case"PPP":n=t.dateTime({width:"long"});break;default:n=t.dateTime({width:"full"})}return n.replace("{{date}}",C(r,t)).replace("{{time}}",T(i,t))}},Y=/^D+$/,H=/^Y+$/,N=["D","DD","YY","YYYY"],q=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,A=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,E=/''/g,J=/[a-zA-Z]/;function F(e,t,n){let a=n?.locale??m.locale??l,r=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??m.firstWeekContainsDate??m.locale?.options?.firstWeekContainsDate??1,i=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??m.weekStartsOn??m.locale?.options?.weekStartsOn??0,o=(0,f.a)(e);if(!((o instanceof Date||"object"==typeof o&&"[object Date]"===Object.prototype.toString.call(o)||"number"==typeof o)&&!isNaN(Number((0,f.a)(o)))))throw RangeError("Invalid time value");let u=t.match(A).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,D[t])(e,a.formatLong):e}).join("").match(q).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(O);return t?t[1].replace(E,"'"):e}(e)};if(W[t])return{isToken:!0,value:e};if(t.match(J))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});a.localize.preprocessor&&(u=a.localize.preprocessor(o,u));let s={firstWeekContainsDate:r,weekStartsOn:i,locale:a};return u.map(r=>{if(!r.isToken)return r.value;let i=r.value;return(!n?.useAdditionalWeekYearTokens&&H.test(i)||!n?.useAdditionalDayOfYearTokens&&Y.test(i))&&function(e,t,n){let a=function(e,t,n){let a="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(console.warn(a),N.includes(e))throw RangeError(a)}(i,t,String(e)),(0,W[i[0]])(o,i,a.localize,s)}).join("")}},94212:(e,t,n)=>{function a(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}n.d(t,{a:()=>a})}};