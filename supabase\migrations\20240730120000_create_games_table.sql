
-- Create the games table
create table public.games (
  id bigint generated by default as identity primary key,
  created_at timestamp with time zone not null default now(),
  title text not null,
  theme text not null,
  game_data jsonb not null,
  stage_id bigint null references public.stages(id) on delete set null
);

-- Enable Row Level Security
alter table public.games enable row level security;

-- Create policies for public access (demo mode)
create policy "Public access for select" on public.games for select using (true);
create policy "Public access for insert" on public.games for insert with check (true);
create policy "Public access for update" on public.games for update using (true);
create policy "Public access for delete" on public.games for delete using (true);
