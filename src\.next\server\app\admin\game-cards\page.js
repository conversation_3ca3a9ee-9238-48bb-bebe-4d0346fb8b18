(()=>{var e={};e.id=911,e.ids=[911],e.modules={2327:()=>{},2727:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,88416,23)),Promise.resolve().then(r.t.bind(r,27342,23)),Promise.resolve().then(r.t.bind(r,74078,23)),Promise.resolve().then(r.t.bind(r,64193,23)),Promise.resolve().then(r.t.bind(r,91573,23)),Promise.resolve().then(r.t.bind(r,95405,23)),Promise.resolve().then(r.t.bind(r,97301,23)),Promise.resolve().then(r.t.bind(r,36159,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7055:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20662:(e,t,r)=>{"use strict";r.d(t,{N:()=>x});var n=r(28625),o=r(64996),s=r(38911),i=r(51731),a=r(73181),c=r(59708);class d extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function l({children:e,isPresent:t}){let r=(0,o.useId)(),s=(0,o.useRef)(null),i=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:a}=(0,o.useContext)(c.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:n,top:o,left:c}=i.current;if(t||!s.current||!e||!n)return;s.current.dataset.motionPopId=r;let d=document.createElement("style");return a&&(d.nonce=a),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${o}px !important;
            left: ${c}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),(0,n.jsx)(d,{isPresent:t,childRef:s,sizeRef:i,children:o.cloneElement(e,{ref:s})})}let u=({children:e,initial:t,isPresent:r,onExitComplete:s,custom:c,presenceAffectsLayout:d,mode:u})=>{let h=(0,i.M)(m),p=(0,o.useId)(),f=(0,o.useCallback)(e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;s&&s()},[h,s]),O=(0,o.useMemo)(()=>({id:p,initial:t,isPresent:r,custom:c,onExitComplete:f,register:e=>(h.set(e,!1),()=>h.delete(e))}),d?[Math.random(),f]:[r,f]);return(0,o.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),o.useEffect(()=>{r||h.size||!s||s()},[r]),"popLayout"===u&&(e=(0,n.jsx)(l,{isPresent:r,children:e})),(0,n.jsx)(a.t.Provider,{value:O,children:e})};function m(){return new Map}var h=r(7054);let p=e=>e.key||"";function f(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var O=r(3618);let x=({children:e,custom:t,initial:r=!0,onExitComplete:a,presenceAffectsLayout:c=!0,mode:d="sync",propagate:l=!1})=>{let[m,x]=(0,h.xQ)(l),N=(0,o.useMemo)(()=>f(e),[e]),j=l&&!m?[]:N.map(p),v=(0,o.useRef)(!0),b=(0,o.useRef)(N),E=(0,i.M)(()=>new Map),[g,_]=(0,o.useState)(N),[U,D]=(0,o.useState)(N);(0,O.E)(()=>{v.current=!1,b.current=N;for(let e=0;e<U.length;e++){let t=p(U[e]);j.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[U,j.length,j.join("-")]);let w=[];if(N!==g){let e=[...N];for(let t=0;t<U.length;t++){let r=U[t],n=p(r);j.includes(n)||(e.splice(t,0,r),w.push(r))}"wait"===d&&w.length&&(e=w),D(f(e)),_(N);return}let{forceRender:C}=(0,o.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:U.map(e=>{let o=p(e),s=(!l||!!m)&&(N===U||j.includes(o));return(0,n.jsx)(u,{isPresent:s,initial:(!v.current||!!r)&&void 0,custom:s?void 0:t,presenceAffectsLayout:c,mode:d,onExitComplete:s?void 0:()=>{if(!E.has(o))return;E.set(o,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==C||C(),D(b.current),l&&(null==x||x()),a&&a())},children:e},o)})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29492:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(64996);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:a="",children:c,iconNode:d,...l},u)=>(0,n.createElement)("svg",{ref:u,...i,width:t,height:t,stroke:e,strokeWidth:o?24*Number(r)/Number(t):r,className:s("lucide",a),...l},[...d.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(c)?c:[c]])),c=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...i},c)=>(0,n.createElement)(a,{ref:c,iconNode:t,className:s(`lucide-${o(e)}`,r),...i}));return r.displayName=`${e}`,r}},31077:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(41808);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},33873:e=>{"use strict";e.exports=require("path")},39103:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},41756:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},48382:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},58031:(e,t,r)=>{Promise.resolve().then(r.bind(r,96244))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65983:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},66279:(e,t,r)=>{Promise.resolve().then(r.bind(r,96442))},70196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>m,tree:()=>d});var n=r(5853),o=r(60554),s=r(30708),i=r.n(s),a=r(8067),c={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>a[e]);r.d(t,c);let d={children:["",{children:["admin",{children:["game-cards",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96244)),"C:\\PL\\COPUN-V5\\src\\app\\admin\\game-cards\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,97898)),"C:\\PL\\COPUN-V5\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,92192,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,82137,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,48358,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,31077))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\PL\\COPUN-V5\\src\\app\\admin\\game-cards\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/game-cards/page",pathname:"/admin/game-cards",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},77190:()=>{},78545:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},78656:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},79551:e=>{"use strict";e.exports=require("url")},87735:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(29492).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},94479:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,49782,23)),Promise.resolve().then(r.t.bind(r,23552,23)),Promise.resolve().then(r.t.bind(r,30708,23)),Promise.resolve().then(r.t.bind(r,17319,23)),Promise.resolve().then(r.t.bind(r,92079,23)),Promise.resolve().then(r.t.bind(r,8487,23)),Promise.resolve().then(r.t.bind(r,55543,23)),Promise.resolve().then(r.t.bind(r,42241,23))},96244:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(20413).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\PL\\\\COPUN-V5\\\\src\\\\app\\\\admin\\\\game-cards\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\PL\\COPUN-V5\\src\\app\\admin\\game-cards\\page.tsx","default")},96442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var n=r(28625),o=r(64996);!function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}();var s=r(39103),i=r(48382),a=r(65983),c=r(78545),d=r(41756),l=r(78656),u=r(87735),m=r(20662),h=r(24824);function p(){let[e,t]=(0,o.useState)([]),[r,a]=(0,o.useState)(!0),{toast:c}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[d,l]=(0,o.useTransition)(),[u,m]=(0,o.useState)(!1),h=async()=>{a(!0),t(await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())()),a(!1)},p=async(e,t)=>{l(async()=>{await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e,t)?(c({title:"Carte cr\xe9\xe9e",description:"La nouvelle carte de jeu a \xe9t\xe9 ajout\xe9e."}),m(!1),await h()):c({title:"Erreur",description:"La cr\xe9ation de la carte a \xe9chou\xe9.",variant:"destructive"})})},O=async(e,t)=>{let r=!1;return l(async()=>{(r=await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e,t))?(c({title:"Carte mise \xe0 jour",description:"Les modifications ont \xe9t\xe9 enregistr\xe9es."}),await h()):c({title:"Erreur",description:"La mise \xe0 jour a \xe9chou\xe9.",variant:"destructive"})}),r},b=e=>{l(async()=>{await Object(function(){var e=Error("Cannot find module '@/app/actions'");throw e.code="MODULE_NOT_FOUND",e}())(e)?(c({title:"Carte supprim\xe9e",description:"La carte a \xe9t\xe9 retir\xe9e de la base de donn\xe9es."}),await h()):c({title:"Erreur",description:"La suppression a \xe9chou\xe9.",variant:"destructive"})})},E=(0,o.useMemo)(()=>e.filter(e=>"triage"===e.type),[e]),g=(0,o.useMemo)(()=>e.filter(e=>"mots"===e.type),[e]),_=(0,o.useMemo)(()=>e.filter(e=>"dilemme"===e.type),[e]),U=(0,o.useMemo)(()=>e.filter(e=>"quizz"===e.type),[e]);return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-foreground font-headline",children:"\xc9diteur de Cartes de Jeu"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Consultez, cr\xe9ez et g\xe9rez les cartes pour les diff\xe9rents types de jeux."})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/create-game-card-form'");throw e.code="MODULE_NOT_FOUND",e}()),{isOpen:u,setIsOpen:m,onCardCreate:p,isCreating:d,children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Cr\xe9er une carte"]})})]}),r?(0,n.jsx)("div",{className:"flex justify-center items-center py-10",children:(0,n.jsx)(i.A,{className:"h-8 w-8 animate-spin text-primary"})}):(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"triage",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full grid-cols-4",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"triage",children:["Vrai/Faux (",E.length,")"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"mots",children:["Mots (",g.length,")"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"dilemme",children:["Dilemmes (",_.length,")"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"quizz",children:["Quizz (",U.length,")"]})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"triage",className:"mt-4",children:(0,n.jsx)(f,{children:E.map(e=>(0,n.jsx)(x,{card:e,onUpdate:O,onDelete:b,isPending:d},e.id))})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"mots",className:"mt-4",children:(0,n.jsx)(f,{children:g.map(e=>(0,n.jsx)(N,{card:e,onUpdate:O,onDelete:b,isPending:d},e.id))})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"dilemme",className:"mt-4",children:(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:_.map(e=>(0,n.jsx)(j,{card:e,onUpdate:O,onDelete:b,isPending:d},e.id))})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"quizz",className:"mt-4",children:(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:U.map(e=>(0,n.jsx)(v,{card:e,onUpdate:O,onDelete:b,isPending:d},e.id))})})]})]})}!function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/create-game-card-form'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/edit-game-card-form'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();let f=({children:e})=>(0,n.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e}),O=({card:e,onUpdate:t,onDelete:r,isPending:o})=>(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"border-t pt-2 mt-2",children:(0,n.jsxs)("div",{className:"flex w-full justify-end gap-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/edit-game-card-form'");throw e.code="MODULE_NOT_FOUND",e}()),{card:e,onCardUpdate:t,isUpdating:o,children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"icon",children:(0,n.jsx)(a.A,{className:"w-4 h-4"})})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{asChild:!0,children:(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"icon",className:"text-destructive hover:text-destructive",children:(0,n.jsx)(c.A,{className:"w-4 h-4"})})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"\xcates-vous s\xfbr ?"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Cette action est irr\xe9versible. La carte sera supprim\xe9e."})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Annuler"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert-dialog'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>r(e.id),disabled:o,children:[o&&(0,n.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"})," Supprimer"]})]})]})]})]})}),x=({card:e,...t})=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:(0,n.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-base flex-1",children:e.statement}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"shrink-0",children:e.theme})]})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:(0,n.jsxs)("p",{className:`font-bold ${e.isTrue?"text-green-600":"text-red-600"}`,children:["R\xe9ponse : ",e.isTrue?"VRAI":"FAUX"]})}),(0,n.jsx)(O,{card:e,...t})]}),N=({card:e,...t})=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:(0,n.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"D\xe9finition"}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-base",children:e.definition})]}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"shrink-0",children:e.theme})]})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:(0,n.jsxs)("p",{className:"font-bold text-primary",children:["R\xe9ponse : ",e.answer]})}),(0,n.jsx)(O,{card:e,...t})]}),j=({card:e,...t})=>{let[r,s]=(0,o.useState)(!1);return(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-base flex-1",children:"Tu pr\xe9f\xe8res..."}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"shrink-0",children:e.theme})]})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-3 flex-grow",children:[(0,n.jsx)("div",{className:"p-2 border-l-4 border-orange-400 bg-orange-50/50",children:(0,n.jsxs)("p",{children:["A) ",e.optionA]})}),(0,n.jsx)("div",{className:"p-2 border-l-4 border-indigo-400 bg-indigo-50/50",children:(0,n.jsxs)("p",{children:["B) ",e.optionB]})}),(0,n.jsx)(m.N,{children:r&&(0,n.jsx)(h.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"overflow-hidden",children:(0,n.jsxs)("div",{className:"p-3 bg-muted/80 rounded-md mt-2",children:[(0,n.jsx)("h4",{className:"font-semibold text-sm mb-1",children:"Le pourquoi du comment :"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:e.explanation})]})})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"link",size:"sm",onClick:()=>s(e=>!e),className:"p-0 h-auto mt-2",children:[(0,n.jsx)(d.A,{className:"mr-2 h-4 w-4"}),r?"Cacher l'explication":"Voir l'explication"]})]}),(0,n.jsx)(O,{card:e,...t})]})},v=({card:e,...t})=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-col",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow",children:(0,n.jsxs)("div",{className:"flex justify-between items-start gap-2",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-base flex-1",children:e.question}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"shrink-0",children:e.theme})]})}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex-grow space-y-2",children:e.answers.map((t,r)=>(0,n.jsxs)("div",{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center gap-2 p-2 rounded-md text-sm",r===e.correctAnswerIndex?"bg-green-100 dark:bg-green-900/30":"bg-muted/50"),children:[r===e.correctAnswerIndex?(0,n.jsx)(l.A,{className:"w-4 h-4 text-green-600"}):(0,n.jsx)(u.A,{className:"w-4 h-4 text-muted-foreground"}),(0,n.jsx)("span",{children:t})]},r))}),(0,n.jsx)(O,{card:e,...t})]})},97898:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>o});var n=r(57307);!function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),r(77190),!function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"Cop'un de la mer",description:"Outil p\xe9dagogique environnemental pour les sports de plein air"};function s({children:e}){return(0,n.jsxs)("html",{lang:"fr",children:[(0,n.jsxs)("head",{children:[(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Plus+Jakarta+Sans:wght@700;800&display=swap",rel:"stylesheet"}),(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1, shrink-to-fit=no"}),(0,n.jsx)("meta",{name:"theme-color",content:"#0ea5e9"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),(0,n.jsx)("meta",{name:"apple-mobile-web-app-title",content:"Cop'un de la mer"}),(0,n.jsx)("link",{rel:"apple-touch-icon",href:"/assets/logoCopun1.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/manifest.json"}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.ico",sizes:"any"})]}),(0,n.jsxs)("body",{className:"font-body antialiased",children:[(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/app-layout'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e}),(0,n.jsx)(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})]})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[683,425,808,824],()=>r(70196));module.exports=n})();